<?php
session_start();
include_once("../../config.php");
$curr = CURRENCY;
$weight = WEIGHT;
$dateformat = DATEFORMAT;
$data = $_SESSION['BinListxls'];
require_once("xlsxwriter.class.php");
require_once('xlsxwriterplus.class.php');
ini_set('display_errors', 0);
ini_set('log_errors', 1);
error_reporting(E_ALL & ~E_NOTICE);
$today = date("m-d-Y");
$data1 = array('Date',$today);
setlocale(LC_MONETARY, 'en_US.UTF-8');
$filename = "BinList.xlsx";
header('Content-disposition: attachment; filename="'.XLSXWriter::sanitize_filename($filename).'"');
header("Content-Type: application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
header('Content-Transfer-Encoding: binary');
header('Cache-Control: must-revalidate');
header('Pragma: public');
include_once("../../connection.php");
setlocale(LC_MONETARY, 'de_DE.UTF-8');
$obj1 =  new Connection();
$connectionlink = Connection::DBConnect();
$connectionlink1 = Connection::DBConnect1();
$datatoday = array('Generated Date',$today);
$datahead = array('Bin List');
//$header = array('Facility','Bin Name','Qty x Part Type','Location','Room','Disposition','Status');
//$header = array('Facility','Bin Name','Qty x Part Type','Mapped Stations','Disposition','Status');
$header = array('Facility','Bin Name','Container Type','Ticket ID','Parent Bin','Location Name','Group Name','Location Type','Disposition','Weight','Seal ID','Description','Status','Mobility Name','Reference Type','Reference ID','Created Date','Destination','Transition Facility','Mapped Stations');

/*$sql = "select c.*,f.FacilityName,l.LocationName,d.disposition,s.Status from facility f,location l,disposition d,custompallet_status s, custompallet c where f.FacilityID = c.FacilityID AND l.LocationID = c.LocationID AND d.disposition_id = c.disposition_id AND s.StatusID = c.StatusID";*/

$sql = "select c.*,f.FacilityName,l.LocationName,g.GroupName,g.LocationType,d.disposition,s.Status,bt.BinType,srt.ReferenceType,p.packageName as ContainerType,v.VendorName as Destination,df.FacilityName as TransitionFacility,
CAST(c.ContainerWeight AS DECIMAL(10,2)) as ContainerWeight from custompallet c
            left join facility f on f.FacilityID = c.FacilityID
            left join location l on l.LocationID = c.LocationID
            LEFT JOIN location_group g on l.GroupID = g.GroupID
            left join disposition d on d.disposition_id = c.disposition_id
            left join custompallet_status s on s.StatusID = c.StatusID
            left join bin_types bt on c.BinTypeID = bt.BinTypeID
            left join shipping_reference_type srt on srt.ReferenceTypeID = c.ReferenceTypeID
            left join package p on p.idPackage = c.idPackage
            left join shipping sh on sh.ShippingID = c.ShippingID
            left join vendor v on v.VendorID = sh.VendorID
            left join facility df on df.FacilityID = sh.DestinationFacilityID
            where  1 ";
if($data[0] && count($data[0]) > 0) {
            foreach ($data[0] as $key => $value) {
                if($value != '') {
                    if($key == 'FacilityName') {
                        $sql = $sql . " AND f.FacilityName like '%".mysqli_real_escape_string($connectionlink1,$value)."%' ";
                    }                   
                    if($key == 'BinName') {
                        $sql = $sql . " AND c.BinName like '%".mysqli_real_escape_string($connectionlink1,$value)."%' ";
                    }
                    if($key == 'ContainerType') {
                        $sql = $sql . " AND p.packageName like '%".mysqli_real_escape_string($connectionlink1,$value)."%' ";
                    }
                    if($key == 'ShippingID') {
                        $sql = $sql . " AND c.ShippingID like '%".mysqli_real_escape_string($connectionlink1,$value)."%' ";
                    }
                    if($key == 'LocationName') {
                        $sql = $sql . " AND l.LocationName like '%".mysqli_real_escape_string($connectionlink1,$value)."%' ";
                    }
                   /* if($key == 'workflow') {
                        $sql = $sql . " AND w.workflow like '%".mysqli_real_escape_string($connectionlink1,$value)."%' ";
                    } */                      
                    if($key == 'Room') {
                        $sql = $sql . " AND c.Room like '".mysqli_real_escape_string($connectionlink1,$value)."%' ";
                    }
                     if($key == 'disposition') {
                        $sql = $sql . " AND d.disposition like '".mysqli_real_escape_string($connectionlink1,$value)."%' ";
                    }
                    if($key == 'Status') {
                        $sql = $sql . " AND s.Status like '".mysqli_real_escape_string($connectionlink1,$value)."%' ";
                    } 
                    if($key == 'MobilityName') {
                        $sql = $sql . " AND c.MobilityName like '".mysqli_real_escape_string($connectionlink1,$value)."%' ";
                    }
                    if($key == 'ParentBinName') {
                        $sql = $sql . " AND c.ParentBinName like '%".mysqli_real_escape_string($connectionlink1,$value)."%' ";
                    }
                    if($key == 'BinType') {
                        $sql = $sql . " AND bt.BinType like '".mysqli_real_escape_string($connectionlink1,$value)."%' ";
                    }
                    if($key == 'ContainerWeight') {
                        $sql = $sql . " AND c.ContainerWeight like '%".mysqli_real_escape_string($connectionlink1,$value)."%' ";
                    }
                    if($key == 'SealID') {
                        $sql = $sql . " AND c.SealID like '%".mysqli_real_escape_string($connectionlink1,$value)."%' ";
                    }
                    if($key == 'Description') {
                        $sql = $sql . " AND c.Description like '%".mysqli_real_escape_string($connectionlink1,$value)."%' ";
                    }
                    if($key == 'GroupName') {
                        $sql = $sql . " AND g.GroupName like '%".mysqli_real_escape_string($connectionlink1,$value)."%' ";
                    }
                    if($key == 'LocationType') {
                        $sql = $sql . " AND g.LocationType like '%".mysqli_real_escape_string($connectionlink1,$value)."%' ";
                    }
                    if($key == 'ReferenceType') {
                        $sql = $sql . " AND srt.ReferenceType like '%".mysqli_real_escape_string($connectionlink1,$value)."%' ";
                    }
                    if($key == 'ReferenceID') {
                        $sql = $sql . " AND c.ReferenceID like '%".mysqli_real_escape_string($connectionlink1,$value)."%' ";
                    }
                    if($key == 'CreatedDate') {
                        $sql = $sql . " AND c.CreatedDate like '%".mysqli_real_escape_string($connectionlink1,$value)."%' ";
                    }
                    if($key == 'Destination') {
                        $sql = $sql . " AND v.VendorName like '%".mysqli_real_escape_string($connectionlink1,$value)."%' ";
                    }
                    if($key == 'TransitionFacility') {
                        $sql = $sql . " AND df.FacilityName like '%".mysqli_real_escape_string($connectionlink1,$value)."%' ";
                    }

                }
                //$sql = $sql . " AND `".$key."` like '%".$value."%' ";
            }
        }
        if($data['OrderBy'] != '') {
            if($data['OrderByType'] == 'asc') {
                $order_by_type = 'asc';
            } else {
                $order_by_type = 'desc';
            }

            if($data['OrderBy'] == 'FacilityName') {
                $sql = $sql . " order by f.FacilityName ".$order_by_type." ";
            } else if($data['OrderBy'] == 'BinName') {
                $sql = $sql . " order by c.BinName ".$order_by_type." ";
            } else if($data['OrderBy'] == 'ContainerType') {
                $sql = $sql . " order by p.packageName ".$order_by_type." ";
            } else if($data['OrderBy'] == 'ShippingID') {
                $sql = $sql . " order by c.ShippingID ".$order_by_type." ";
            } else if($data['OrderBy'] == 'LocationName') {
                $sql = $sql . " order by l.LocationName ".$order_by_type." ";
            } /*else if($data['OrderBy'] == 'workflow') {
                $sql = $sql . " order by w.workflow ".$order_by_type." ";
            }*/ else if($data['OrderBy'] == 'Room') {
                $sql = $sql . " order by c.Room ".$order_by_type." ";
            } else if($data['OrderBy'] == 'disposition') {
                $sql = $sql . " order by d.disposition ".$order_by_type." ";
            } else if($data['OrderBy'] == 'Status') {
                $sql = $sql . " order by s.Status ".$order_by_type." ";
            } else if($data['OrderBy'] == 'MobilityName') {
                $sql = $sql . " order by c.MobilityName ".$order_by_type." ";
            } else if($data['OrderBy'] == 'ParentBinName') {
                $sql = $sql . " order by c.ParentBinName ".$order_by_type." ";
            } else if($data['OrderBy'] == 'BinType') {
                $sql = $sql . " order by bt.BinType ".$order_by_type." ";
            } else if($data['OrderBy'] == 'ContainerWeight') {
                $sql = $sql . " order by c.ContainerWeight ".$order_by_type." ";
            } else if($data['OrderBy'] == 'SealID') {
                $sql = $sql . " order by c.SealID ".$order_by_type." ";
            } else if($data['OrderBy'] == 'Description') {
                $sql = $sql . " order by c.Description ".$order_by_type." ";
            } else if($data['OrderBy'] == 'GroupName') {
                $sql = $sql . " order by g.GroupName ".$order_by_type." ";
            } else if($data['OrderBy'] == 'LocationType') {
                $sql = $sql . " order by g.LocationType ".$order_by_type." ";
            } else if($data['OrderBy'] == 'ReferenceType') {
                $sql = $sql . " order by srt.ReferenceType ".$order_by_type." ";
            } else if($data['OrderBy'] == 'ReferenceID') {
                $sql = $sql . " order by c.ReferenceID ".$order_by_type." ";
            } else if($data['OrderBy'] == 'CreatedDate') {
                $sql = $sql . " order by c.CreatedDate ".$order_by_type." ";
            } else if($data['OrderBy'] == 'Destination') {
                $sql = $sql . " order by v.VendorName ".$order_by_type." ";
            } else if($data['OrderBy'] == 'TransitionFacility') {
                $sql = $sql . " order by df.FacilityName ".$order_by_type." ";
            }

           
        } else {
            $sql = $sql . " order by f.FacilityName desc ";
        }
$query = mysqli_query($connectionlink1,$sql);
if(mysqli_error($connectionlink1)) {
    echo mysqli_error($connectionlink1);    
}
            while($row = mysqli_fetch_assoc($query)) {

                $parttype = '';				
				// $sqlpalitems = "Select count(*) as palquantity, CC.part_type from asset PI, catlog_creation CC
				// WHERE 
				// CC.mpn_id = PI.UniversalModelNumber
				// AND PI.CustomPalletID = '".$row['CustomPalletID']."' 
				// GROUP BY CC.part_type";

                // $sqlpalitems = "Select count(*) as palquantity, PI.part_type from asset PI	WHERE  PI.CustomPalletID = '".$row['CustomPalletID']."' GROUP BY PI.part_type";

				// $querypalitems = mysqli_query($connectionlink1,$sqlpalitems);
				// while($rowpalitems = mysqli_fetch_assoc($querypalitems)) {
				// 	$parttype = $parttype." ".$rowpalitems['palquantity']."-".$rowpalitems['part_type']."\n";
				// }
				// $row['parttype'] = $parttype;


                //Start get linked Stations
				$mapped_stations = '';
				$query_1 = "select s.SiteName from station_custompallet_mapping m,site s where m.SiteID = s.SiteID and m.CustomPalletID = '".$row['CustomPalletID']."' ";
				$q_1 = mysqli_query($connectionlink1,$query_1);
				if(mysqli_affected_rows($connectionlink1) > 0) {
					while($row_1 = mysqli_fetch_assoc($q_1)) {
						$mapped_stations = $mapped_stations ." ".$row_1['SiteName'].",";
					}
				}
				$row['MappedStations'] = $mapped_stations;
				//End get linked Stations


                //$row2  = array($row['FacilityName'],$row['BinName'],$row['parttype'],$row['LocationName'],$row['Room'],$row['disposition'],$row['Status']);
                //$row2  = array($row['FacilityName'],$row['BinName'],$row['parttype'],$row['MappedStations'],$row['disposition'],$row['Status']);
                $row2  = array(
                    $row['FacilityName'],
                    $row['BinName'],
                    $row['ContainerType'],
                    $row['ShippingID'],
                    $row['ParentBinName'],
                    $row['LocationName'],
                    $row['GroupName'],
                    $row['LocationType'],
                    $row['disposition'],
                    $row['ContainerWeight'],
                    $row['SealID'],
                    $row['Description'],
                    $row['Status'],
                    $row['MobilityName'],
                    $row['ReferenceType'],
                    $row['ReferenceID'],
                    date('Y-m-d', strtotime($row['CreatedDate'])),
                    $row['Destination'],
                    $row['TransitionFacility'],
                    $row['MappedStations']
                );
                $rows[] = $row2;
            }

$sheet_name = 'Bin List';
$style1 = array( ['font-style'=>'bold'],['font-style'=>'']);
$writer = new XLSWriterPlus();
$writer->setAuthor('eViridis');
$writer->markMergedCell($sheet_name, $start_row = 0, $start_col = 0, $end_row = 2, $end_col = 19);
$writer->writeSheetRow($sheet_name, $datahead, $col_options = ['font-style'=>'bold','font-size'=>20,'halign'=>'center','valign'=>'center']);
$writer->writeSheetRow($sheet_name, array(''), $col_options = ['font-style'=>'bold']);
$writer->writeSheetRow($sheet_name, array(''), $col_options = ['font-style'=>'bold']);
$writer->writeSheetRow($sheet_name, array(''), $col_options = ['font-style'=>'bold']);
$writer->writeSheetRow($sheet_name, $header, $col_options = ['font-style'=>'bold', 'border'=>'left,right,top,bottom','halign'=>'center','valign'=>'center','fill'=>'#eee']);
foreach($rows as $row11)
    $writer->writeSheetRow($sheet_name, $row11 , $col_options = ['border'=>'left,right,top,bottom','halign'=>'left']);
$writer->writeToStdOut();
exit(0);
?> 