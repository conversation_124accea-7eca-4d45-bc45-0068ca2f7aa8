<?php
session_start();
include_once("admin.class.php");
class OverrideClass extends AdminClass {

    public function GetAllDispositions ($data) {
		if(!isset($_SESSION['user'])) {
			$json['Success'] = false;
			$json['Result'] = 'Login to continue';
			return json_encode($json);
		}
		$json = array(
			'Success' => false,
			'Result' => 'No Data'
		);	
		try {
			// if(! $this->isPermitted($_SESSION['user']['ProfileID'],'Disposition Override')) {
			// 	$json['Success'] = false;
			// 	$json['Result'] = 'No Access to Disposition Override Page';
			// 	return json_encode($json);
			// }
            
            $sql = "Select disposition_id, disposition,sub_disposition from disposition where status='Active' and eligible_for_disposition_override = '1' order by disposition";            
            $query = mysqli_query($this->connectionlink,$sql);
            if(mysqli_error($this->connectionlink)) {
                $json['Success'] = false;
                $json['Result'] = mysqli_error($this->connectionlink);
                return json_encode($json);
            } else {
                if(mysqli_affected_rows($this->connectionlink) > 0) {
                    $i = 0;
                    while($row = mysqli_fetch_assoc($query)) {
                        $result[$i] = $row;
                        $i++;
                    }
                    $json['Success'] = true;
                    $json['Result'] = $result;
                } else {
                    $json['Success'] = false;
                    $json['Result'] = 'No Results';
                }
            }
            return json_encode($json);
            
		} catch (Exception $e) {
			$json['Success'] = false;
			$json['Result'] = $e->getMessage();
			return json_encode($json);
		}
	}   


    public function UpdateSerialDisposition ($data) {
		if(!isset($_SESSION['user'])) {
			$json['Success'] = false;
			$json['Result'] = 'Login to continue';
			return json_encode($json);
		}
		$json = array(
			'Success' => false,
			'Result' => 'No data'
		);	
		try {
			if(! $this->isPermitted($_SESSION['user']['ProfileID'],'Disposition Override')) {
				$json['Success'] = false;
				$json['Result'] = 'No Access to Disposition Override Page';
				return json_encode($json);
			}

            if(! $this->isWritePermitted($_SESSION['user']['ProfileID'],'Disposition Override')) {
                $json['Success'] = false;
                $json['Result'] = 'You have Read only Access to Disposition Override Page';
                return json_encode($json);
            }

            //Start validate Disposition
            $query16 = "select disposition from disposition where disposition_id = '".mysqli_real_escape_string($this->connectionlink,$data['disposition_id'])."'";
            $q16 = mysqli_query($this->connectionlink,$query16);
            if(mysqli_error($this->connectionlink)) {			
                $json['Success'] = false;
                $json['Result'] = mysqli_error($this->connectionlink);
                return json_encode($json);
            }			
            if(mysqli_affected_rows($this->connectionlink) > 0) {
                $disposition = mysqli_fetch_assoc($q16);
            } else {
                $json['Success'] = false;
                $json['Result'] = 'Invalid Disposition';
                return json_encode($json);
            }
            //End validate Disposition

            //Start validate BIN

            $query = "select * from custompallet where BinName = '".mysqli_real_escape_string($this->connectionlink,$data['BinName'])."'";
            $q = mysqli_query($this->connectionlink,$query);
            if(mysqli_error($this->connectionlink)) {			
                $json['Success'] = false;
                $json['Result'] = mysqli_error($this->connectionlink);
                return json_encode($json);
            }			
            if(mysqli_affected_rows($this->connectionlink) > 0) {
                $row = mysqli_fetch_assoc($q);
                if($row['InventoryBased'] == '1') {
                    $json['Success'] = false;
                    $json['Result'] = 'BIN is dedicated for Sub Component';
                    return json_encode($json);
                }
                if($row['StatusID'] != '1') {					
                    $json['Success'] = false;
                    $json['Result'] = 'BIN Status is not Active';
                    return json_encode($json);
                }

                if($row['FacilityID'] != $_SESSION['user']['FacilityID']) {									
                    $json['Success'] = false;
                    $json['Result'] = 'BIN Facility is different from Users Facility';
                    return json_encode($json);
                }
                if($row['disposition_id'] != $data['disposition_id']) {
                    $json['Success'] = false;
                    $json['Result'] = 'BIN disposition is different from New Disposition';
                    return json_encode($json);
                }
            } else {
                $json['Success'] = false;
                $json['Result'] = 'Invalid BIN Name';
                return json_encode($json);
            }
            //End validate BIN

            //Start validate Requester Login
            $query6 = "select * from users where UserName = '".mysqli_real_escape_string($this->connectionlink,$data['RequesterLogin'])."'";
            $q6 = mysqli_query($this->connectionlink,$query6);
			if(mysqli_error($this->connectionlink)) {
				$json['Success'] = false;
				$json['Result'] = mysqli_error($this->connectionlink);
				return json_encode($json);
			}
			if(mysqli_affected_rows($this->connectionlink) > 0) {
				$requester = mysqli_fetch_assoc($q6);
				if($requester['Status'] != 1) {
					$json['Success'] = false;
					$json['Result'] = 'Requester Status is not Active';
					return json_encode($json);
				}
			} else {
				$json['Success'] = false;
				$json['Result'] = 'Invalid Requester Login';
				return json_encode($json);
			}
            //End validate Requester Login

            //Start Validate serial Number
			//$query20 = "select * from asset where SerialNumber = '".mysqli_real_escape_string($this->connectionlink,$data['SerialNumber'])."'";
            $query20 = "select * from asset where SerialNumber = '".mysqli_real_escape_string($this->connectionlink,$data['SerialNumber'])."' and StatusID not in (6,8)";
			$q20 = mysqli_query($this->connectionlink,$query20);
			if(mysqli_error($this->connectionlink)) {
				$json['Success'] = false;
				$json['Result'] = mysqli_error($this->connectionlink);
				return json_encode($json);
			}
			if(mysqli_affected_rows($this->connectionlink) > 0) {
				$asset = mysqli_fetch_assoc($q20);
				if($asset['StatusID'] != 1) {
					$json['Success'] = false;
					$json['Result'] = 'Serial Number Status is not Active';
					return json_encode($json);
				}
			} else {
				$json['Success'] = false;
				$json['Result'] = 'Invalid Serial Number';
				return json_encode($json);
			}
			//End Validate serial Number

            if($data['disposition_id'] == '4' && ! $data['rule_id']) {
                $json['Success'] = false;
				$json['Result'] = 'Invalid Business Rule ID';
				return json_encode($json);
            }
            
            //Start moving Serial to New BIN and updating new disposition
            $query1 = "update asset set CustomPalletID = '".mysqli_real_escape_string($this->connectionlink,$row['CustomPalletID'])."',DateUpdated = NOW(),UpdatedBy = '".$_SESSION['user']['UserId']."',disposition_id = '".mysqli_real_escape_string($this->connectionlink,$data['disposition_id'])."',DispositionOverrideReason = '".mysqli_real_escape_string($this->connectionlink,$data['OverrideReason'])."' ";
            if($data['disposition_id'] == '4') {
                $query1 = $query1. ",RecentDispositionDate = NOW(),RecentDispositionRuleID = '".mysqli_real_escape_string($this->connectionlink,$data['rule_id'])."',RecentDispositionBy = '".$_SESSION['user']['UserId']."',RecentDispositionComments = 'Updated in Disposition Override' ";
            }
            $query1 = $query1. " where AssetScanID = '".mysqli_real_escape_string($this->connectionlink,$asset['AssetScanID'])."'";
			$q1 = mysqli_query($this->connectionlink,$query1);
			if(mysqli_error($this->connectionlink)) {
				$json['Success'] = false;
				$json['Result'] = mysqli_error($this->connectionlink);
				return json_encode($json);
			}

            $query2 = "update custompallet_items set CustomPalletID = '".mysqli_real_escape_string($this->connectionlink,$row['CustomPalletID'])."' where AssetScanID = '".mysqli_real_escape_string($this->connectionlink,$asset['AssetScanID'])."'";
			$q2 = mysqli_query($this->connectionlink,$query2);
			if(mysqli_error($this->connectionlink)) {
				$json['Success'] = false;
				$json['Result'] = mysqli_error($this->connectionlink);
				return json_encode($json);
			}

            $query3 = "UPDATE `custompallet` SET `AssetsCount`= `AssetsCount` + 1 WHERE `CustomPalletID`='".mysqli_real_escape_string($this->connectionlink,$row['CustomPalletID'])."'";
			$q3 = mysqli_query($this->connectionlink,$query3);
			if(mysqli_error($this->connectionlink)) {
				$json['Success'] = false;
				$json['Result'] = mysqli_error($this->connectionlink);
				return json_encode($json);
			}

			$query4 = "UPDATE `custompallet` SET `AssetsCount`= `AssetsCount` - 1 WHERE `CustomPalletID`='".mysqli_real_escape_string($this->connectionlink,$asset['CustomPalletID'])."'";
			$q4 = mysqli_query($this->connectionlink,$query4);
			if(mysqli_error($this->connectionlink)) {
				$json['Success'] = false;
				$json['Result'] = mysqli_error($this->connectionlink);
				return json_encode($json);
			}
            //End moving Serial to New BIN and updating new disposition


            //Insert into Asset Tracking
			$desc = "Asset Disposition (New Disposition : ".$disposition['disposition'].") updated and moved to BIN, (BIN ID : ".$row['BinName']."),(Requester Login : ".$data['RequesterLogin'].") in Disposition Override Screen";
			$query3 = "insert into asset_tracking (AssetScanID,Action,Description,UniqueID,CreatedDate,CreatedBy) values ('".mysqli_real_escape_string($this->connectionlink,$asset['AssetScanID'])."','".mysqli_real_escape_string($this->connectionlink,$desc)."','','',NOW(),'".$_SESSION['user']['UserId']."')";
			$q3 = mysqli_query($this->connectionlink,$query3);
			if(mysqli_error($this->connectionlink)) {
				$json['Success'] = false;
				$json['Result'] = mysqli_error($this->connectionlink);
				return json_encode($json);
			}
			//End Inserting into Asset Tracking

            //Start insert in Disposition Override table
            $query4 = "insert into disposition_override_assets (AssetScanID,SerialNumber,FromCustomPalletID,ToCustomPalletID,FromDispositionID,ToDispositionID,OverrideReason,Comments,CreatedDate,CreatedBy,RequesterLogin) values ('".mysqli_real_escape_string($this->connectionlink,$asset['AssetScanID'])."','".mysqli_real_escape_string($this->connectionlink,$asset['SerialNumber'])."','".mysqli_real_escape_string($this->connectionlink,$asset['CustomPalletID'])."','".mysqli_real_escape_string($this->connectionlink,$row['CustomPalletID'])."','".mysqli_real_escape_string($this->connectionlink,$asset['disposition_id'])."','".mysqli_real_escape_string($this->connectionlink,$row['disposition_id'])."','".mysqli_real_escape_string($this->connectionlink,$data['OverrideReason'])."','Disposition Override',NOW(),'".$_SESSION['user']['UserId']."','".mysqli_real_escape_string($this->connectionlink,$data['RequesterLogin'])."')";
            $q4 = mysqli_query($this->connectionlink,$query4);
			if(mysqli_error($this->connectionlink)) {
				$json['Success'] = false;
				$json['Result'] = mysqli_error($this->connectionlink);
				return json_encode($json);
			}
            //End insert in Disposition Override table

            $json['Success'] = true;
			$json['Result'] = 'Disposition updated and moved to BIN ';
            return json_encode($json);
		} catch (Exception $e) {
			$json['Success'] = false;
			$json['Result'] = $e->getMessage();
			return json_encode($json);
		}
	}

	public function UpdateBulkDisposition ($data) {
		if(!isset($_SESSION['user'])) {
			$json['Success'] = false;
			$json['Result'] = 'Login to continue';
			return json_encode($json);
		}
		$json = array(
			'Success' => false,
			'Result' => 'No data'
		);	
		try {
			if(! $this->isPermitted($_SESSION['user']['ProfileID'],'Disposition Override')) {
				$json['Success'] = false;
				$json['Result'] = 'No Access to Disposition Override Page';
				return json_encode($json);
			}

            if(! $this->isWritePermitted($_SESSION['user']['ProfileID'],'Disposition Override')) {
                $json['Success'] = false;
                $json['Result'] = 'You have Read only Access to Disposition Override Page';
                return json_encode($json);
            }

            //Start validate Disposition
            $query16 = "select disposition from disposition where disposition_id = '".mysqli_real_escape_string($this->connectionlink,$data['disposition_id_bulk'])."'";
            $q16 = mysqli_query($this->connectionlink,$query16);
            if(mysqli_error($this->connectionlink)) {			
                $json['Success'] = false;
                $json['Result'] = mysqli_error($this->connectionlink);
                return json_encode($json);
            }			
            if(mysqli_affected_rows($this->connectionlink) > 0) {
                $disposition = mysqli_fetch_assoc($q16);
            } else {
                $json['Success'] = false;
                $json['Result'] = 'Invalid Disposition';
                return json_encode($json);
            }
            //End validate Disposition

            //Start validate TOBIN
            $query = "select * from custompallet where BinName = '".mysqli_real_escape_string($this->connectionlink,$data['ToBinName'])."'";
            $q = mysqli_query($this->connectionlink,$query);
            if(mysqli_error($this->connectionlink)) {			
                $json['Success'] = false;
                $json['Result'] = mysqli_error($this->connectionlink);
                return json_encode($json);
            }			
            if(mysqli_affected_rows($this->connectionlink) > 0) {
                $to_cp = mysqli_fetch_assoc($q);
                if($to_cp['InventoryBased'] == '1') {
                    $json['Success'] = false;
                    $json['Result'] = 'To BIN is dedicated for Sub Component';
                    return json_encode($json);
                }
                if($to_cp['StatusID'] != '1') {				
                    $json['Success'] = false;
                    $json['Result'] = 'To BIN Status is not Active';
                    return json_encode($json);
                }

                if($to_cp['FacilityID'] != $_SESSION['user']['FacilityID']) {									
                    $json['Success'] = false;
                    $json['Result'] = 'To BIN Facility is different from Users Facility';
                    return json_encode($json);
                }
                if($to_cp['disposition_id'] != $data['disposition_id_bulk']) {
                    $json['Success'] = false;
                    $json['Result'] = 'To BIN disposition is different from New Disposition';
                    return json_encode($json);
                }
            } else {
                $json['Success'] = false;
                $json['Result'] = 'Invalid To BIN Name';
                return json_encode($json);
            }
            //End validate TOBIN


			//Start validate FROM BIN
            $query2 = "select * from custompallet where BinName = '".mysqli_real_escape_string($this->connectionlink,$data['FromBinName'])."'";
            $q2 = mysqli_query($this->connectionlink,$query2);
            if(mysqli_error($this->connectionlink)) {			
                $json['Success'] = false;
                $json['Result'] = mysqli_error($this->connectionlink);
                return json_encode($json);
            }			
            if(mysqli_affected_rows($this->connectionlink) > 0) {
                $from_cp = mysqli_fetch_assoc($q2);
                if($from_cp['InventoryBased'] == '1') {
                    $json['Success'] = false;
                    $json['Result'] = 'From BIN is dedicated for Sub Component';
                    return json_encode($json);
                }
                if($from_cp['StatusID'] != '1') {				
                    $json['Success'] = false;
                    $json['Result'] = 'From BIN Status is not Active';
                    return json_encode($json);
                }

                if($from_cp['FacilityID'] != $_SESSION['user']['FacilityID']) {									
                    $json['Success'] = false;
                    $json['Result'] = 'From BIN Facility is different from Users Facility';
                    return json_encode($json);
                }                
            } else {
                $json['Success'] = false;
                $json['Result'] = 'Invalid From BIN Name';
                return json_encode($json);
            }
            //End validate FROM BIN


			if($from_cp['CustomPalletID'] == $to_cp['CustomPalletID']) {
				$json['Success'] = false;
                $json['Result'] = "From and To Bins can't be same";
                return json_encode($json);
			}


             //Start validate Requester Login
             $query6 = "select * from users where UserName = '".mysqli_real_escape_string($this->connectionlink,$data['RequesterLogin_bulk'])."'";
             $q6 = mysqli_query($this->connectionlink,$query6);
             if(mysqli_error($this->connectionlink)) {
                 $json['Success'] = false;
                 $json['Result'] = mysqli_error($this->connectionlink);
                 return json_encode($json);
             }
             if(mysqli_affected_rows($this->connectionlink) > 0) {
                 $requester = mysqli_fetch_assoc($q6);
                 if($requester['Status'] != 1) {
                     $json['Success'] = false;
                     $json['Result'] = 'Requester Status is not Active';
                     return json_encode($json);
                 }
             } else {
                 $json['Success'] = false;
                 $json['Result'] = 'Invalid Requester Login';
                 return json_encode($json);
             }
             //End validate Requester Login
			
			//Start consolidating BINs

            if($data['disposition_id_bulk'] == '4' && ! $data['rule_id_bulk']) {
                $json['Success'] = false;
				$json['Result'] = 'Invalid Business Rule ID';
				return json_encode($json);
            }

            //Insert into Asset Tracking			
			$desc = "Asset Disposition (New Disposition : ".$disposition['disposition'].") updated and moved to BIN, (BIN ID : ".$to_cp['BinName']."),(Requester Login : ".$data['RequesterLogin_bulk'].") in Disposition Override Bulk of Disposition Override Screen";
			//$query3 = "insert into asset_tracking (AssetScanID,Action,Description,UniqueID,CreatedDate,CreatedBy) values ('".mysqli_real_escape_string($this->connectionlink,$asset['AssetScanID'])."','".mysqli_real_escape_string($this->connectionlink,$desc)."','','',NOW(),'".$_SESSION['user']['UserId']."')";
            $query3 = "insert into asset_tracking (AssetScanID,Action,Description,UniqueID,CreatedDate,CreatedBy) 
            select AssetScanID,'".mysqli_real_escape_string($this->connectionlink,$desc)."','','',NOW(),'".$_SESSION['user']['UserId']."' from custompallet_items where CustomPalletID = '".mysqli_real_escape_string($this->connectionlink,$from_cp['CustomPalletID'])."' and AssetScanID > 0";
			$q3 = mysqli_query($this->connectionlink,$query3);
			if(mysqli_error($this->connectionlink)) {
				$json['Success'] = false;
				$json['Result'] = mysqli_error($this->connectionlink);
				return json_encode($json);
			}
			if(mysqli_affected_rows($this->connectionlink) > 0) {

			} else {
				$json['Success'] = false;
				$json['Result'] = 'No Serials available in From BIN';
				return json_encode($json);
			}
			//End Inserting into Asset Tracking

            //Insert into Disposition Override table			
            $query5 = "insert into disposition_override_assets (AssetScanID,SerialNumber,FromCustomPalletID,ToCustomPalletID,FromDispositionID,ToDispositionID,OverrideReason,Comments,CreatedDate,CreatedBy,RequesterLogin) 
            select AssetScanID,SerialNumber,CustomPalletID,'".$to_cp['CustomPalletID']."',disposition_id,'".$data['disposition_id_bulk']."','".$data['OverrideReason_bulk']."','Disposition Override Bulk',NOW(),'".$_SESSION['user']['UserId']."','".mysqli_real_escape_string($this->connectionlink,$data['RequesterLogin_bulk'])."' from asset where CustomPalletID = '".mysqli_real_escape_string($this->connectionlink,$from_cp['CustomPalletID'])."' ";            
			$q5 = mysqli_query($this->connectionlink,$query5);
			if(mysqli_error($this->connectionlink)) {
				$json['Success'] = false;
				$json['Result'] = mysqli_error($this->connectionlink);
				return json_encode($json);
			}			
			//End Insert into Disposition Override table	



            //Start update Asset
			$query1 = "update asset set CustomPalletID = '".mysqli_real_escape_string($this->connectionlink,$to_cp['CustomPalletID'])."',disposition_id = '".mysqli_real_escape_string($this->connectionlink,$data['disposition_id_bulk'])."',DateUpdated = NOW(),UpdatedBy = '".$_SESSION['user']['UserId']."',DispositionOverrideReason = '".mysqli_real_escape_string($this->connectionlink,$data['OverrideReason_bulk'])."' ";
            if($data['disposition_id_bulk'] == '4') {
                $query1 = $query1. ",RecentDispositionDate = NOW(),RecentDispositionRuleID = '".mysqli_real_escape_string($this->connectionlink,$data['rule_id_bulk'])."',RecentDispositionBy = '".$_SESSION['user']['UserId']."',RecentDispositionComments = 'Updated in Disposition Override Bulk' ";
            }
            $query1 = $query1." where CustomPalletID = '".mysqli_real_escape_string($this->connectionlink,$from_cp['CustomPalletID'])."'";
			$q1 = mysqli_query($this->connectionlink,$query1);
			if(mysqli_error($this->connectionlink)) {
				$json['Success'] = false;
				$json['Result'] = mysqli_error($this->connectionlink);
				return json_encode($json);
			}			
			//End update Asset

			//Start update Custom Pallet Items
			$query2 = "update custompallet_items set CustomPalletID = '".mysqli_real_escape_string($this->connectionlink,$to_cp['CustomPalletID'])."' where CustomPalletID = '".mysqli_real_escape_string($this->connectionlink,$from_cp['CustomPalletID'])."' and AssetScanID > 0";
			$q2 = mysqli_query($this->connectionlink,$query2);
			if(mysqli_error($this->connectionlink)) {
				$json['Success'] = false;
				$json['Result'] = mysqli_error($this->connectionlink);
				return json_encode($json);
			}
			//End update Custom Pallet Items

			//Start update Custom Pallet Counts
            $query33 = "select count(*) from custompallet_items where CustomPalletID = '".mysqli_real_escape_string($this->connectionlink,$to_cp['CustomPalletID'])."' ";
            $q33 = mysqli_query($this->connectionlink,$query33);
			if(mysqli_error($this->connectionlink)) {
				$json['Success'] = false;
				$json['Result'] = mysqli_error($this->connectionlink);
				return json_encode($json);
			}
            if(mysqli_affected_rows($this->connectionlink) > 0) {
                $row33 = mysqli_fetch_assoc($q33);
                $query3 = "UPDATE `custompallet` SET `AssetsCount`= '".mysqli_real_escape_string($this->connectionlink,$row33['count(*)'])."' WHERE `CustomPalletID`='".mysqli_real_escape_string($this->connectionlink,$to_cp['CustomPalletID'])."'";
                $q3 = mysqli_query($this->connectionlink,$query3);
                if(mysqli_error($this->connectionlink)) {
                    $json['Success'] = false;
                    $json['Result'] = mysqli_error($this->connectionlink);
                    return json_encode($json);
                }
            }			

			$query4 = "UPDATE `custompallet` SET `AssetsCount`= 0 WHERE `CustomPalletID`='".mysqli_real_escape_string($this->connectionlink,$from_cp['CustomPalletID'])."'";
			$q4 = mysqli_query($this->connectionlink,$query4);
			if(mysqli_error($this->connectionlink)) {
				$json['Success'] = false;
				$json['Result'] = mysqli_error($this->connectionlink);
				return json_encode($json);
			}
			//End update Custom Pallet Counts

            //End consolidating BINs

			$json['Success'] = true;
			$json['Result'] = 'Disposition updated and moved to BIN ';
            return json_encode($json);
			
		} catch (Exception $e) {
			$json['Success'] = false;
			$json['Result'] = $e->getMessage();
			return json_encode($json);
		}
	}


    public function GetBusinessRuleNames ($data) {
		if(!isset($_SESSION['user'])) {
			$json['Success'] = false;
			$json['Result'] = 'Login to continue';
			return json_encode($json);
		}
		$json = array(
			'Success' => false,
			'Result' => 'No Data'
		);	
		try {
			if(! $this->isPermitted($_SESSION['user']['ProfileID'],'Disposition Override')) {
				$json['Success'] = false;
				$json['Result'] = 'No Access to Disposition Override Page';
				return json_encode($json);
			}            
            $sql = "select b.rule_name,b.rule_id from business_rule b,business_rule_versions v where b.version_id = v.version_id and v.current_version = '1' and b.status = 'Active' and b.rule_name like '%Sanitization%' order by b.rule_name";
            $query = mysqli_query($this->connectionlink,$sql);
            if(mysqli_error($this->connectionlink)) {
                $json['Success'] = false;
                $json['Result'] = mysqli_error($this->connectionlink);
                return json_encode($json);
            } else {
                if(mysqli_affected_rows($this->connectionlink) > 0) {
                    $i = 0;
                    while($row = mysqli_fetch_assoc($query)) {
                        $result[$i] = $row;
                        $i++;
                    }
                    $json['Success'] = true;
                    $json['Result'] = $result;
                } else {
                    $json['Success'] = false;
                    $json['Result'] = 'No Results';
                }
            }
            return json_encode($json);
            
		} catch (Exception $e) {
			$json['Success'] = false;
			$json['Result'] = $e->getMessage();
			return json_encode($json);
		}
	}

    public function GetLoggedinUserDetails ($data) {
		if(!isset($_SESSION['user'])) {
			$json['Success'] = false;
			$json['Result'] = 'Login to continue';
			return json_encode($json);
		}
		$json = array(
			'Success' => true,
			'Result' => $_SESSION['user']
		);
        return json_encode($json);
	}


    public function CheckDispositionOverrideEligibility ($data) {
		if(!isset($_SESSION['user'])) {
			$json['Success'] = false;
			$json['Result'] = 'Login to continue';
			return json_encode($json);
		}
		$json = array(
			'Success' => false,
			'Result' => 'No Data'
		);	
		try {			                        
            $query = "select count(*) from disposition_override_eligibility where FacilityID = '".$_SESSION['user']['FacilityID']."' and DispositionFrom = '".mysqli_real_escape_string($this->connectionlink,$data['DispositionFrom'])."' and DispositionTo = '".mysqli_real_escape_string($this->connectionlink,$data['DispositionTo'])."' and (EligibilityType = '".mysqli_real_escape_string($this->connectionlink,$data['EligibilityType'])."' or EligibilityType = 'All Levels' ) and Status = 'Active'";
            $q = mysqli_query($this->connectionlink,$query);
            if(mysqli_error($this->connectionlink)) {
                $json['Success'] = false;
                $json['Result'] = mysqli_error($this->connectionlink);
                return json_encode($json);
            } else {
                if(mysqli_affected_rows($this->connectionlink) > 0) {
                    $row = mysqli_fetch_assoc($q);
                    if($row['count(*)'] > 0) {
                        $json['Success'] = false;
                        $json['Result'] = "Facility, 'Disposition From' and 'Disposition To' combination exists in Disposition Override Ineligibility List";
                        return json_encode($json);                        
                    } else {
                        $json['Success'] = true;
                        $json['Result'] = 'Eligible';
                        return json_encode($json);  
                    }                    
                } else {
                    $json['Success'] = false;
                    $json['Result'] = 'Invalid';
                    return json_encode($json);
                }
            }
            return json_encode($json);
            
		} catch (Exception $e) {
			$json['Success'] = false;
			$json['Result'] = $e->getMessage();
			return json_encode($json);
		}
	}


    public function ValidateBinDisposition ($data) {
		if(!isset($_SESSION['user'])) {
			$json['Success'] = false;
			$json['Result'] = 'Login to continue';
			return json_encode($json);
		}
		$json = array(
			'Success' => false,
			'Result' => 'No Data'
		);	
		try {		
            $query = "select * from custompallet where BinName = '".mysqli_real_escape_string($this->connectionlink,$data['BinName'])."'";
            $q = mysqli_query($this->connectionlink,$query);
            if(mysqli_error($this->connectionlink)) {
                $json['Success'] = false;
                $json['Result'] = mysqli_error($this->connectionlink);
                return json_encode($json);
            } else {
                if(mysqli_affected_rows($this->connectionlink) > 0) {
                    $row = mysqli_fetch_assoc($q);
                    if($row['InventoryBased'] == '1') {
                        $json['Success'] = false;
                        $json['Result'] = 'BIN is dedicated for Sub Component';
                        return json_encode($json);
                    }
                    if($row['StatusID'] != '1') {					
                        $json['Success'] = false;
                        $json['Result'] = 'BIN Status is not Active';
                        return json_encode($json);
                    }
    
                    if($row['FacilityID'] != $_SESSION['user']['FacilityID']) {									
                        $json['Success'] = false;
                        $json['Result'] = 'BIN Facility is different from Users Facility';
                        return json_encode($json);
                    }
                    // Allow bins with AssetsCount = 0 and disposition_id = null for DispositionTo
                    if($row['disposition_id'] != $data['DispositionTo']) {
                        // Check if bin is empty and has no disposition (null)
                        if($row['AssetsCount'] == 0 && ($row['disposition_id'] == null || $row['disposition_id'] == '' || $row['disposition_id'] == '0')) {
                            // Empty bin with no disposition - allow and mark for disposition update
                            $row['RequiresDispositionUpdate'] = true;
                        } else {
                            $json['Success'] = false;
                            $json['Result'] = "BIN disposition is different from 'Disposition To'";
                            return json_encode($json);
                        }
                    }

                    // Add Max Limit validation (only if MaxLimitRequired = 1)
                    if($row['MaxLimitRequired'] == '1') {
                        $currentAssetsInBin = intval($row['AssetsCount']);
                        $maxAssetsAllowed = intval($row['MaximumAssets']);

                        // Check if destination bin has enough space for one more asset
                        if(($currentAssetsInBin + 1) > $maxAssetsAllowed) {
                            $json['Success'] = false;
                            $json['Result'] = "BIN does not have enough space. Current count: $currentAssetsInBin, Maximum capacity: $maxAssetsAllowed";
                            return json_encode($json);
                        }
                    }

                    // Add Customer Lock validation
                    if($row['CustomerLock'] == '1') {
                        // We'll need to validate the customer match when the serial is processed
                        // Mark this bin as requiring customer validation
                        $row['RequiresCustomerValidation'] = true;
                    }

                    $json['Success'] = true;
                    $json['Result'] = $row;
                    return json_encode($json);

                } else {
                    $json['Success'] = false;
                    $json['Result'] = 'Invalid Bin Name';
                    return json_encode($json);
                }
            }
            return json_encode($json);
            
		} catch (Exception $e) {
			$json['Success'] = false;
			$json['Result'] = $e->getMessage();
			return json_encode($json);
		}
	}


    public function GetDispositionOverrideReasons ($data) {
		if(!isset($_SESSION['user'])) {
			$json['Success'] = false;
			$json['Result'] = 'Login to continue';
			return json_encode($json);
		}
		$json = array(
			'Success' => false,
			'Result' => 'No Data'
		);	
		try {
			// if(! $this->isPermitted($_SESSION['user']['ProfileID'],'Disposition Override')) {
			// 	$json['Success'] = false;
			// 	$json['Result'] = 'No Access to Disposition Override Page';
			// 	return json_encode($json);
			// }
            
            $sql = "Select * from disposition_override_reason where Status='Active' order by OverrideReason";            
            $query = mysqli_query($this->connectionlink,$sql);
            if(mysqli_error($this->connectionlink)) {
                $json['Success'] = false;
                $json['Result'] = mysqli_error($this->connectionlink);
                return json_encode($json);
            } else {
                if(mysqli_affected_rows($this->connectionlink) > 0) {
                    $i = 0;
                    while($row = mysqli_fetch_assoc($query)) {
                        $result[$i] = $row;
                        $i++;
                    }
                    $json['Success'] = true;
                    $json['Result'] = $result;
                } else {
                    $json['Success'] = false;
                    $json['Result'] = 'No Disposition Override Reasons Available';
                }
            }
            return json_encode($json);
            
		} catch (Exception $e) {
			$json['Success'] = false;
			$json['Result'] = $e->getMessage();
			return json_encode($json);
		}
	}



    public function ValidateSerial ($data) {
		if(!isset($_SESSION['user'])) {
			$json['Success'] = false;
			$json['Result'] = 'Login to continue';
			return json_encode($json);
		}
		$json = array(
			'Success' => false,
			'Result' => $data
		);            
		try {
			if(! $this->isPermitted($_SESSION['user']['ProfileID'],'Disposition Override Assets')) {
				$json['Success'] = false;
				$json['Result'] = 'No Access to Disposition Override Assets Page';
				return json_encode($json);
			}

            if(! $this->isWritePermitted($_SESSION['user']['ProfileID'],'Disposition Override Assets')) {
                $json['Success'] = false;
                $json['Result'] = 'You have Read only Access to Disposition Override Assets Page';
                return json_encode($json);
            }

            //Start validate serial Number
            $query = "select a.*,ast.Status,d.disposition as from_disposition from asset a 
            left join asset_status ast on a.StatusID = ast.StatusID 
            left join disposition d on a.disposition_id = d.disposition_id 
            where a.SerialNumber = '".mysqli_real_escape_string($this->connectionlink,$data['SerialNumber'])."'";

            $query = "select a.*,ast.Status,d.disposition as from_disposition from asset a 
            left join asset_status ast on a.StatusID = ast.StatusID 
            left join disposition d on a.disposition_id = d.disposition_id 
            where a.SerialNumber = '".mysqli_real_escape_string($this->connectionlink,$data['SerialNumber'])."' AND (a.StatusID = 1 or a.StatusID = 9)";
			$q = mysqli_query($this->connectionlink,$query);
			if(mysqli_error($this->connectionlink)) {
				$json['Success'] = false;
				$json['Result'] = mysqli_error($this->connectionlink);
				return json_encode($json);
			}
			if(mysqli_affected_rows($this->connectionlink) > 0) {
				$asset = mysqli_fetch_assoc($q);
				// if($asset['StatusID'] != 1) {
				// 	$json['Success'] = false;
				// 	$json['Result'] = 'Serial Number Status is '.$asset['Status'];
				// 	return json_encode($json);
				// }
                if($asset['FacilityID'] != $_SESSION['user']['FacilityID']) {
                    $json['Success'] = false;
					$json['Result'] = 'Asset Facility is different from User Facility';
					return json_encode($json);
                }
                if($asset['disposition_id'] != $data['FromDispositionID']) {
                    $json['Success'] = false;
					$json['Result'] = "Asset Disposition is different from 'Disposition From'";
					return json_encode($json);
                }
			} else {
				$json['Success'] = false;
				$json['Result'] = 'Invalid Serial Number or Serial is not Active';
				return json_encode($json);
			}
			//End Validate serial Number

            //Start validate Bin
            $query = "select c.*,d.disposition as to_disposition from custompallet c 
            left join disposition d on c.disposition_id = d.disposition_id 
            where c.CustomPalletID = '".mysqli_real_escape_string($this->connectionlink,$data['ToCustomPalletID'])."'";
            $q = mysqli_query($this->connectionlink,$query);
			if(mysqli_error($this->connectionlink)) {
				$json['Success'] = false;
				$json['Result'] = mysqli_error($this->connectionlink);
				return json_encode($json);
			}
			if(mysqli_affected_rows($this->connectionlink) > 0) {
				$CustomPallet = mysqli_fetch_assoc($q);
                if($CustomPallet['FacilityID'] != $_SESSION['user']['FacilityID']) {
                    $json['Success'] = false;
					$json['Result'] = 'Bin Facility is different from User Facility';
					return json_encode($json);
                }
            } else {
                $json['Success'] = false;
				$json['Result'] = 'Invalid To Bin';
				return json_encode($json);
            }
            //End validate Bin

            if($data['ToDispositionID'] == '4' && ! $data['rule_id']) {
                $json['Success'] = false;
				$json['Result'] = 'Invalid Business Rule ID';
				return json_encode($json);
            }

            //Start get Override reason deatails
            $query2 = "select ReasonID,OverrideReason from disposition_override_reason where ReasonID = '".mysqli_real_escape_string($this->connectionlink,$data['OverrideReason'])."'";
            $q2 = mysqli_query($this->connectionlink,$query2);
			if(mysqli_error($this->connectionlink)) {
				$json['Success'] = false;
				$json['Result'] = mysqli_error($this->connectionlink);
				return json_encode($json);
			}
			if(mysqli_affected_rows($this->connectionlink) > 0) {
                $row2 = mysqli_fetch_assoc($q2);
            } else {
                $json['Success'] = false;
				$json['Result'] = 'Invalid Override Reason';
				return json_encode($json);
            }
            //ENd get Override reason deails

            //Start check if serial already exists in override records for this OverrideID
            $query_check = "select RecordID from disposition_override_records where OverrideID = '".mysqli_real_escape_string($this->connectionlink,$data['OverrideID'])."' and SerialNumber = '".mysqli_real_escape_string($this->connectionlink,$asset['SerialNumber'])."'";
            $q_check = mysqli_query($this->connectionlink,$query_check);
            if(mysqli_error($this->connectionlink)) {
                $json['Success'] = false;
                $json['Result'] = mysqli_error($this->connectionlink);
                return json_encode($json);
            }
            if(mysqli_affected_rows($this->connectionlink) > 0) {
                $json['Success'] = false;
                $json['Result'] = 'Serial Number already scanned for this override';
                return json_encode($json);
            }
            //End check if serial already exists

            //Start Customer Lock Validation
            if($CustomPallet['CustomerLock'] == '1') {
                // Get the asset's customer ID
                $assetCustomerID = null;
                if(isset($asset['AWSCustomerID']) && !empty($asset['AWSCustomerID'])) {
                    $assetCustomerID = $asset['AWSCustomerID'];
                }

                // If asset has no customer ID, skip validation (allow placement)
                if($assetCustomerID != null && $assetCustomerID != '' && $assetCustomerID != '0') {
                    // Get destination bin's customer
                    $destinationCustomerID = $CustomPallet['AWSCustomerID'];

                    // Check if destination bin is empty (no customer assigned yet)
                    if($destinationCustomerID == NULL || $destinationCustomerID == '' || $destinationCustomerID == '0') {
                        // Destination bin is empty, assign it to the asset's customer
                        $custQuery = "SELECT Customer FROM aws_customers WHERE AWSCustomerID = '".mysqli_real_escape_string($this->connectionlink,$assetCustomerID)."'";
                        $custResult = mysqli_query($this->connectionlink, $custQuery);
                        $customerName = 'Unknown Customer';
                        if(mysqli_affected_rows($this->connectionlink) > 0) {
                            $custRow = mysqli_fetch_assoc($custResult);
                            $customerName = $custRow['Customer'];
                        }

                        // Update destination bin's customer
                        $updateBinQuery = "UPDATE custompallet SET AWSCustomerID = '".mysqli_real_escape_string($this->connectionlink,$assetCustomerID)."' WHERE CustomPalletID = '".mysqli_real_escape_string($this->connectionlink,$CustomPallet['CustomPalletID'])."'";
                        $updateBinResult = mysqli_query($this->connectionlink, $updateBinQuery);
                        if(mysqli_error($this->connectionlink)) {
                            $json['Success'] = false;
                            $json['Result'] = mysqli_error($this->connectionlink);
                            return json_encode($json);
                        }

                        // Add tracking record
                        $trackingAction = "Bin customer assigned to " . $customerName . " when adding serial " . $asset['SerialNumber'] . " via disposition override";
                        $trackingQuery = "INSERT INTO custompallet_tracking (CustomPalletID, BinName, Action, CreatedDate, CreatedBy) VALUES ('".mysqli_real_escape_string($this->connectionlink,$CustomPallet['CustomPalletID'])."', '".mysqli_real_escape_string($this->connectionlink,$CustomPallet['BinName'])."', '".mysqli_real_escape_string($this->connectionlink,$trackingAction)."', NOW(), '".$_SESSION['user']['UserId']."')";
                        mysqli_query($this->connectionlink, $trackingQuery);

                        // Update the CustomPallet array for later use
                        $CustomPallet['AWSCustomerID'] = $assetCustomerID;
                    } else {
                        // Destination bin has a customer, validate asset customer matches
                        if($destinationCustomerID != $assetCustomerID) {
                            // Get customer names for error message
                            $destCustQuery = "SELECT Customer FROM aws_customers WHERE AWSCustomerID = '".mysqli_real_escape_string($this->connectionlink,$destinationCustomerID)."'";
                            $destCustResult = mysqli_query($this->connectionlink, $destCustQuery);
                            $destCustomerName = 'Unknown Customer';
                            if(mysqli_affected_rows($this->connectionlink) > 0) {
                                $destCustRow = mysqli_fetch_assoc($destCustResult);
                                $destCustomerName = $destCustRow['Customer'];
                            }

                            $assetCustQuery = "SELECT Customer FROM aws_customers WHERE AWSCustomerID = '".mysqli_real_escape_string($this->connectionlink,$assetCustomerID)."'";
                            $assetCustResult = mysqli_query($this->connectionlink, $assetCustQuery);
                            $assetCustomerName = 'Unknown Customer';
                            if(mysqli_affected_rows($this->connectionlink) > 0) {
                                $assetCustRow = mysqli_fetch_assoc($assetCustResult);
                                $assetCustomerName = $assetCustRow['Customer'];
                            }

                            $json['Success'] = false;
                            $json['Result'] = 'Bin is locked to ' . $destCustomerName . ', but serial belongs to ' . $assetCustomerName . '. Cannot add serial to bin.';
                            return json_encode($json);
                        }
                    }
                }
            }
            //End Customer Lock Validation

            //Start Bin Disposition Update for empty bins with no disposition
            if($CustomPallet['AssetsCount'] == 0 && ($CustomPallet['disposition_id'] == null || $CustomPallet['disposition_id'] == '' || $CustomPallet['disposition_id'] == '0')) {
                // Get the disposition name for tracking
                $dispQuery = "SELECT disposition FROM disposition WHERE disposition_id = '".mysqli_real_escape_string($this->connectionlink,$data['ToDispositionID'])."'";
                $dispResult = mysqli_query($this->connectionlink, $dispQuery);
                $dispositionName = 'Unknown Disposition';
                if(mysqli_affected_rows($this->connectionlink) > 0) {
                    $dispRow = mysqli_fetch_assoc($dispResult);
                    $dispositionName = $dispRow['disposition'];
                }

                // Update bin's disposition to match the asset's disposition
                $updateBinDispQuery = "UPDATE custompallet SET disposition_id = '".mysqli_real_escape_string($this->connectionlink,$data['ToDispositionID'])."' WHERE CustomPalletID = '".mysqli_real_escape_string($this->connectionlink,$CustomPallet['CustomPalletID'])."'";
                $updateBinDispResult = mysqli_query($this->connectionlink, $updateBinDispQuery);
                if(mysqli_error($this->connectionlink)) {
                    $json['Success'] = false;
                    $json['Result'] = mysqli_error($this->connectionlink);
                    return json_encode($json);
                }

                // Add tracking record for disposition update
                $trackingAction = "Bin disposition updated to " . $dispositionName . " when adding serial " . $asset['SerialNumber'] . " via disposition override";
                $trackingQuery = "INSERT INTO custompallet_tracking (CustomPalletID, BinName, Action, CreatedDate, CreatedBy) VALUES ('".mysqli_real_escape_string($this->connectionlink,$CustomPallet['CustomPalletID'])."', '".mysqli_real_escape_string($this->connectionlink,$CustomPallet['BinName'])."', '".mysqli_real_escape_string($this->connectionlink,$trackingAction)."', NOW(), '".$_SESSION['user']['UserId']."')";
                mysqli_query($this->connectionlink, $trackingQuery);

                // Update the CustomPallet array for later use
                $CustomPallet['disposition_id'] = $data['ToDispositionID'];
                $CustomPallet['to_disposition'] = $dispositionName;
            }
            //End Bin Disposition Update

            //Start insert in disposition override deatils
            $query4 = "insert into disposition_override_records (OverrideID, AssetScanID, SerialNumber, MPN, part_type, FromDispositionID, FromDispositionName, ToDispositionID, ToDispositionName, FromCustomPalletID, ToCustomPalletID, ToBinName, ReasonID, OverrideReason, CreatedDate, CreatedBy,notes_scan_time) values ('".mysqli_real_escape_string($this->connectionlink,$data['OverrideID'])."','".mysqli_real_escape_string($this->connectionlink,$asset['AssetScanID'])."','".mysqli_real_escape_string($this->connectionlink,$asset['SerialNumber'])."','".mysqli_real_escape_string($this->connectionlink,$asset['UniversalModelNumber'])."','".mysqli_real_escape_string($this->connectionlink,$asset['part_type'])."','".mysqli_real_escape_string($this->connectionlink,$data['FromDispositionID'])."','".mysqli_real_escape_string($this->connectionlink,$asset['from_disposition'])."','".mysqli_real_escape_string($this->connectionlink,$CustomPallet['disposition_id'])."','".mysqli_real_escape_string($this->connectionlink,$CustomPallet['to_disposition'])."','".mysqli_real_escape_string($this->connectionlink,$asset['CustomPalletID'])."','".mysqli_real_escape_string($this->connectionlink,$CustomPallet['CustomPalletID'])."','".mysqli_real_escape_string($this->connectionlink,$CustomPallet['BinName'])."','".mysqli_real_escape_string($this->connectionlink,$row2['ReasonID'])."','".mysqli_real_escape_string($this->connectionlink,$row2['OverrideReason'])."',NOW(),'".$_SESSION['user']['UserId']."','".mysqli_real_escape_string($this->connectionlink,$data['notes_scan_time'])."')";
            $q4 = mysqli_query($this->connectionlink,$query4);
			if(mysqli_error($this->connectionlink)) {
				$json['Success'] = false;
				$json['Result'] = mysqli_error($this->connectionlink);
				return json_encode($json);
			}

            $RecordID = mysqli_insert_id($this->connectionlink);
            //End insert in disposition override details
            
            $asset['CustomPallet'] = $CustomPallet;
            $asset['OverrideReason'] = $row2;
            $result = array();

            $result['AssetScanID'] = $asset['AssetScanID'];
            $result['SerialNumber'] = $asset['SerialNumber'];
            $result['MPN'] = $asset['UniversalModelNumber'];
            $result['part_type'] = $asset['part_type'];
            $result['FromDisposition'] = $asset['from_disposition'];
            $result['FromDispositionID'] = $asset['disposition_id'];
            $result['FromCustomPalletID'] = $asset['CustomPalletID'];

            $result['ToDisposition'] = $CustomPallet['to_disposition'];
            $result['ToDispositionID'] = $CustomPallet['disposition_id'];
            $result['ToCustomPalletID'] = $CustomPallet['CustomPalletID'];            
            $result['ToBinName'] = $CustomPallet['BinName'];

            $result['OverrideReason'] = $row2['OverrideReason'];
            $result['ReasonID'] = $row2['ReasonID'];
            $result['RecordID'] = $RecordID;

            $json['Success'] = true;
            $json['Result'] = $result;
            return json_encode($json);
		} catch (Exception $e) {
			$json['Success'] = false;
			$json['Result'] = $e->getMessage();
			return json_encode($json);
		}
	}



    public function CreateDispositionOverride ($data) {
		if(!isset($_SESSION['user'])) {
			$json['Success'] = false;
			$json['Result'] = 'Login to continue';
			return json_encode($json);
		}
		$json = array(
			'Success' => false,
			'Result' => $data
		);
        //return json_encode($json);
		try {
			if(! $this->isPermitted($_SESSION['user']['ProfileID'],$data['PageName'])) {
				$json['Success'] = false;
				$json['Result'] = 'No Access to '.$data['PageName'].' Page';
				return json_encode($json);
			}

            if(! $this->isWritePermitted($_SESSION['user']['ProfileID'],$data['PageName'])) {
                $json['Success'] = false;
                $json['Result'] = 'You have Read only Access to '.$data['PageName'].' Page';
                return json_encode($json);
            }  
            
            if(empty($data['notes_scan_time'])){
                $serialScanTime = $this->GetDBCurrentTime();
                $serialScanTime = json_decode($serialScanTime,TRUE);
                $data['notes_scan_time'] = $serialScanTime['Result'];
            }

            $query = "insert into disposition_override (OverrideType,FromDispositionID,ToDispositionID,RequesterLogin,ToCustomPalletID,ToBinName,ReasonID,OverrideNotes,CreatedDate,CreatedBy,notes_scan_time) values ('".mysqli_real_escape_string($this->connectionlink,$data['OverrideType'])."','".mysqli_real_escape_string($this->connectionlink,$data['FromDispositionID'])."','".mysqli_real_escape_string($this->connectionlink,$data['ToDispositionID'])."','".mysqli_real_escape_string($this->connectionlink,$data['RequesterLogin'])."','".mysqli_real_escape_string($this->connectionlink,$data['ToCustomPalletID'])."','".mysqli_real_escape_string($this->connectionlink,$data['BinName'])."','".mysqli_real_escape_string($this->connectionlink,$data['OverrideReason'])."','".mysqli_real_escape_string($this->connectionlink,$data['Notes'])."',NOW(),'".$_SESSION['user']['UserId']."','".mysqli_real_escape_string($this->connectionlink,$data['notes_scan_time'])."')";
			$q = mysqli_query($this->connectionlink,$query);
			if(mysqli_error($this->connectionlink)) {
				$json['Success'] = false;
				$json['Result'] = mysqli_error($this->connectionlink);
				return json_encode($json);
			}

            $OverrideID = mysqli_insert_id($this->connectionlink);
			
            $this->ValidateUserInputNotes($data['Notes']);

            $json['Success'] = true;
            $json['OverrideID'] = $OverrideID;
            return json_encode($json);            
		} catch (Exception $e) {
			$json['Success'] = false;
			$json['Result'] = $e->getMessage();
			return json_encode($json);
		}
	}


    public function DeleteOverrideSerial ($data) {
		if(!isset($_SESSION['user'])) {
			$json['Success'] = false;
			$json['Result'] = 'Login to continue';
			return json_encode($json);
		}
		$json = array(
			'Success' => false,
			'Result' => $data
		);        
		try {
			if(! $this->isPermitted($_SESSION['user']['ProfileID'],'Disposition Override Assets')) {
				$json['Success'] = false;
				$json['Result'] = 'No Access to Disposition Override Assets Page';
				return json_encode($json);
			}

            if(! $this->isWritePermitted($_SESSION['user']['ProfileID'],'Disposition Override Assets')) {
                $json['Success'] = false;
                $json['Result'] = 'You have Read only Access to Disposition Override Assets Page';
                return json_encode($json);
            }

            $query = "delete from disposition_override_records where RecordID = '".mysqli_real_escape_string($this->connectionlink,$data['RecordID'])."'";
            $q = mysqli_query($this->connectionlink,$query);
			if(mysqli_error($this->connectionlink)) {
				$json['Success'] = false;
				$json['Result'] = mysqli_error($this->connectionlink);
				return json_encode($json);
			}
            $json['Success'] = true;
            $json['Result'] = 'Serial Deleted';
            return json_encode($json);            
		} catch (Exception $e) {
			$json['Success'] = false;
			$json['Result'] = $e->getMessage();
			return json_encode($json);
		}
	}


    public function CompleteDispositionOverrideAssets ($data) {
		if(!isset($_SESSION['user'])) {
			$json['Success'] = false;
			$json['Result'] = 'Login to continue';
			return json_encode($json);
		}
		$json = array(
			'Success' => false,
			'Result' => $data
		);
        //return json_encode($json);
		try {
			if(! $this->isPermitted($_SESSION['user']['ProfileID'],'Disposition Override Assets')) {
				$json['Success'] = false;
				$json['Result'] = 'No Access to Disposition Override Assets Page';
				return json_encode($json);
			}

            if(! $this->isWritePermitted($_SESSION['user']['ProfileID'],'Disposition Override Assets')) {
                $json['Success'] = false;
                $json['Result'] = 'You have Read only Access to Disposition Override Assets Page';
                return json_encode($json);
            }
            $event_id = rand(1000000000, 9999999999);
            //Start get ToCustomPallet Details
            $query = "select d.*,cp.* from disposition_override d,custompallet cp where d.OverrideID = '".mysqli_real_escape_string($this->connectionlink,$data['OverrideID'])."' and d.ToCustomPalletID = cp.CustomPalletID ";
            $q = mysqli_query($this->connectionlink,$query);
			if(mysqli_error($this->connectionlink)) {
				$json['Success'] = false;
				$json['Result'] = mysqli_error($this->connectionlink);
				return json_encode($json);
			}
            if(mysqli_affected_rows($this->connectionlink) > 0) {
                $row = mysqli_fetch_assoc($q);
                if($row['StatusID'] != '1') {
                    $json['Success'] = false;
                    $json['Result'] = 'To Bin Status is not Active';
                    return json_encode($json);    
                }
                $available = $row['MaximumAssets'] - $row['AssetsCount'];
            } else {
                $json['Success'] = false;
				$json['Result'] = 'Invalid To Bin Details';
				return json_encode($json);
            }
            //End get ToCustomPallet Details

            //Start check If To Bin is capable of taking all scanned records (only if MaxLimitRequired = 1)
            $query2 = "select count(*) from disposition_override_records r where r.OverrideID = '".mysqli_real_escape_string($this->connectionlink,$data['OverrideID'])."' ";
            $q2 = mysqli_query($this->connectionlink,$query2);
			if(mysqli_error($this->connectionlink)) {
				$json['Success'] = false;
				$json['Result'] = mysqli_error($this->connectionlink);
				return json_encode($json);
			}
            if(mysqli_affected_rows($this->connectionlink) > 0) {
                $row2 = mysqli_fetch_assoc($q2);

                // Only validate capacity if MaxLimitRequired = 1
                if($row['MaxLimitRequired'] == '1') {
                    if($row2['count(*)'] > $available) {
                        $json['Success'] = false;
                        $json['Result'] = "Not enough space in To Bin ( Available for ".$available." Serials)";
                        return json_encode($json);
                    }
                }
            } else {
                $json['Success'] = false;
				$json['Result'] = 'Invalid';
				return json_encode($json);
            }
            //End check If To Bin is capable of taking all scanned records
            
            //Start getting records
            $query6 = "select * from disposition_override_records r where r.OverrideID = '".mysqli_real_escape_string($this->connectionlink,$data['OverrideID'])."' ";
            $q6 = mysqli_query($this->connectionlink,$query6);
			if(mysqli_error($this->connectionlink)) {
				$json['Success'] = false;
				$json['Result'] = mysqli_error($this->connectionlink);
				return json_encode($json);
			}
            $totalRecords = mysqli_num_rows($q6);
			//if(mysqli_affected_rows($this->connectionlink)>0) {
			if($totalRecords > 0) {
				if($totalRecords == '1') {
					$batch_event_flag = 'N';
				} else {
					$batch_event_flag = 'Y';
				}
                while($row6 = mysqli_fetch_assoc($q6)) {

                    //Start moving Serial to New BIN and updating new disposition
                    $query1 = "update asset set CustomPalletID = '".mysqli_real_escape_string($this->connectionlink,$row['CustomPalletID'])."',DateUpdated = NOW(),UpdatedBy = '".$_SESSION['user']['UserId']."',disposition_id = '".mysqli_real_escape_string($this->connectionlink,$row6['ToDispositionID'])."',DispositionOverrideReason = '".mysqli_real_escape_string($this->connectionlink,$row6['OverrideReason'])."' ";
                    if($data['ToDispositionID'] == '4') {
                        $query1 = $query1. ",RecentDispositionDate = NOW(),RecentDispositionRuleID = '".mysqli_real_escape_string($this->connectionlink,$data['rule_id'])."',RecentDispositionBy = '".$_SESSION['user']['UserId']."',RecentDispositionComments = 'Updated in Disposition Override Assets' ";
                    }
                    $query1 = $query1. " where AssetScanID = '".mysqli_real_escape_string($this->connectionlink,$row6['AssetScanID'])."'";
                    $q1 = mysqli_query($this->connectionlink,$query1);
                    if(mysqli_error($this->connectionlink)) {
                        $json['Success'] = false;
                        $json['Result'] = mysqli_error($this->connectionlink);
                        return json_encode($json);
                    }

                    $query2 = "update custompallet_items set CustomPalletID = '".mysqli_real_escape_string($this->connectionlink,$row['CustomPalletID'])."' where AssetScanID = '".mysqli_real_escape_string($this->connectionlink,$row6['AssetScanID'])."'";
                    $q2 = mysqli_query($this->connectionlink,$query2);
                    if(mysqli_error($this->connectionlink)) {
                        $json['Success'] = false;
                        $json['Result'] = mysqli_error($this->connectionlink);
                        return json_encode($json);
                    }

                    $query3 = "UPDATE `custompallet` SET `AssetsCount`= `AssetsCount` + 1 WHERE `CustomPalletID`='".mysqli_real_escape_string($this->connectionlink,$row['CustomPalletID'])."'";
                    $q3 = mysqli_query($this->connectionlink,$query3);
                    if(mysqli_error($this->connectionlink)) {
                        $json['Success'] = false;
                        $json['Result'] = mysqli_error($this->connectionlink);
                        return json_encode($json);
                    }

                    $query4 = "UPDATE `custompallet` SET `AssetsCount`= `AssetsCount` - 1 WHERE `CustomPalletID`='".mysqli_real_escape_string($this->connectionlink,$row6['FromCustomPalletID'])."'";
                    $q4 = mysqli_query($this->connectionlink,$query4);
                    if(mysqli_error($this->connectionlink)) {
                        $json['Success'] = false;
                        $json['Result'] = mysqli_error($this->connectionlink);
                        return json_encode($json);
                    }
                    //End moving Serial to New BIN and updating new disposition


                    //Insert into Asset Tracking
                    $desc = "Asset Disposition (New Disposition : ".$row6['ToDispositionName'].") updated and moved to BIN, (BIN ID : ".$row6['ToBinName']."),(Requester Login : ".$row['RequesterLogin'].") in Disposition Override Assets Screen";
                    $query3 = "insert into asset_tracking (AssetScanID,Action,Description,UniqueID,CreatedDate,CreatedBy) values ('".mysqli_real_escape_string($this->connectionlink,$row6['AssetScanID'])."','".mysqli_real_escape_string($this->connectionlink,$desc)."','','',NOW(),'".$_SESSION['user']['UserId']."')";
                    $q3 = mysqli_query($this->connectionlink,$query3);
                    if(mysqli_error($this->connectionlink)) {
                        $json['Success'] = false;
                        $json['Result'] = mysqli_error($this->connectionlink);
                        return json_encode($json);
                    }
                    //End Inserting into Asset Tracking

                    //Start insert in Disposition Override table
                    $query4 = "insert into disposition_override_assets (AssetScanID,SerialNumber,FromCustomPalletID,ToCustomPalletID,FromDispositionID,ToDispositionID,OverrideReason,Comments,CreatedDate,CreatedBy,RequesterLogin,Notes,TPVRController,OverrideType,notes_scan_time,event_id,batch_event_flag) values ('".mysqli_real_escape_string($this->connectionlink,$row6['AssetScanID'])."','".mysqli_real_escape_string($this->connectionlink,$row6['SerialNumber'])."','".mysqli_real_escape_string($this->connectionlink,$row6['FromCustomPalletID'])."','".mysqli_real_escape_string($this->connectionlink,$row6['ToCustomPalletID'])."','".mysqli_real_escape_string($this->connectionlink,$row6['FromDispositionID'])."','".mysqli_real_escape_string($this->connectionlink,$row6['ToDispositionID'])."','".mysqli_real_escape_string($this->connectionlink,$row6['OverrideReason'])."','Disposition Override Assets',NOW(),'".$_SESSION['user']['UserId']."','".mysqli_real_escape_string($this->connectionlink,$row['RequesterLogin'])."','".mysqli_real_escape_string($this->connectionlink,$row['OverrideNotes'])."','".mysqli_real_escape_string($this->connectionlink,$data['AuditController'])."','SN-Level	','".mysqli_real_escape_string($this->connectionlink,$row6['notes_scan_time'])."','".mysqli_real_escape_string($this->connectionlink,$event_id)."','".mysqli_real_escape_string($this->connectionlink,$batch_event_flag)."')";
                    $q4 = mysqli_query($this->connectionlink,$query4);
                    if(mysqli_error($this->connectionlink)) {
                        $json['Success'] = false;
                        $json['Result'] = mysqli_error($this->connectionlink);
                        return json_encode($json);
                    }
                    //End insert in Disposition Override table

                    //Start update override records with completed
                    $query5 = "update disposition_override_records set Completed = '1',TPVRController = '".mysqli_real_escape_string($this->connectionlink,$data['AuditController'])."',CompletedDate = NOW(),CompletedBy = '".$_SESSION['user']['UserId']."' where RecordID = '".mysqli_real_escape_string($this->connectionlink,$row6['RecordID'])."'";
                    $q5 = mysqli_query($this->connectionlink,$query5);
                    if(mysqli_error($this->connectionlink)) {
                        $json['Success'] = false;
                        $json['Result'] = mysqli_error($this->connectionlink);
                        return json_encode($json);
                    }
                    //End update override records with completed
                    
                }
            } else {
                $json['Success'] = false;
				$json['Result'] = 'No Serials Available';
				return json_encode($json);
            }
            //End getting records

            // Check if bin needs disposition update (empty bin with no disposition that now has assets)
            if($row['AssetsCount'] == 0 && ($row['disposition_id'] == null || $row['disposition_id'] == '' || $row['disposition_id'] == '0')) {
                // Get the disposition name for tracking
                $dispQuery = "SELECT disposition FROM disposition WHERE disposition_id = '".mysqli_real_escape_string($this->connectionlink,$data['ToDispositionID'])."'";
                $dispResult = mysqli_query($this->connectionlink, $dispQuery);
                $dispositionName = 'Unknown Disposition';
                if(mysqli_affected_rows($this->connectionlink) > 0) {
                    $dispRow = mysqli_fetch_assoc($dispResult);
                    $dispositionName = $dispRow['disposition'];
                }

                // Update bin's disposition to match the assets' disposition
                $updateBinDispQuery = "UPDATE custompallet SET disposition_id = '".mysqli_real_escape_string($this->connectionlink,$data['ToDispositionID'])."' WHERE CustomPalletID = '".mysqli_real_escape_string($this->connectionlink,$row['CustomPalletID'])."'";
                $updateBinDispResult = mysqli_query($this->connectionlink, $updateBinDispQuery);
                if(mysqli_error($this->connectionlink)) {
                    $json['Success'] = false;
                    $json['Result'] = mysqli_error($this->connectionlink);
                    return json_encode($json);
                }

                // Add tracking record for disposition update
                $trackingAction = "Bin disposition updated to " . $dispositionName . " after completing disposition override batch";
                $trackingQuery = "INSERT INTO custompallet_tracking (CustomPalletID, BinName, Action, CreatedDate, CreatedBy) VALUES ('".mysqli_real_escape_string($this->connectionlink,$row['CustomPalletID'])."', '".mysqli_real_escape_string($this->connectionlink,$row['BinName'])."', '".mysqli_real_escape_string($this->connectionlink,$trackingAction)."', NOW(), '".$_SESSION['user']['UserId']."')";
                mysqli_query($this->connectionlink, $trackingQuery);
            }

            $json['Success'] = true;
            $json['Result'] = 'You have successfully overrode '.$row2['count(*)'].' assets.All records can be found in WorkStation Search and Tracking';
            return json_encode($json);
		} catch (Exception $e) {
			$json['Success'] = false;
			$json['Result'] = $e->getMessage();
			return json_encode($json);
		}
	}

    public function ValidateFromBinDisposition ($data) {
		if(!isset($_SESSION['user'])) {
			$json['Success'] = false;
			$json['Result'] = 'Login to continue';
			return json_encode($json);
		}
		$json = array(
			'Success' => false,
			'Result' => 'No Data'
		);	
		try {		
            $query = "select * from custompallet where BinName = '".mysqli_real_escape_string($this->connectionlink,$data['BinName'])."'";
            $q = mysqli_query($this->connectionlink,$query);
            if(mysqli_error($this->connectionlink)) {
                $json['Success'] = false;
                $json['Result'] = mysqli_error($this->connectionlink);
                return json_encode($json);
            } else {
                if(mysqli_affected_rows($this->connectionlink) > 0) {
                    $row = mysqli_fetch_assoc($q);
                    if($row['InventoryBased'] == '1') {
                        $json['Success'] = false;
                        $json['Result'] = 'BIN is dedicated for Sub Component';
                        return json_encode($json);
                    }
                    if($row['StatusID'] != '1') {					
                        $json['Success'] = false;
                        $json['Result'] = 'BIN Status is not Active';
                        return json_encode($json);
                    }
    
                    if($row['FacilityID'] != $_SESSION['user']['FacilityID']) {									
                        $json['Success'] = false;
                        $json['Result'] = 'BIN Facility is different from Users Facility';
                        return json_encode($json);
                    }
                    if($row['disposition_id'] != $data['DispositionFrom']) {
                        $json['Success'] = false;
                        $json['Result'] = "BIN disposition is different from 'Disposition From'";
                        return json_encode($json);
                    }

                    $json['Success'] = true;
                    $json['Result'] = $row;
                    return json_encode($json);

                } else {
                    $json['Success'] = false;
                    $json['Result'] = 'Invalid Bin Name';
                    return json_encode($json);
                }
            }
            return json_encode($json);
            
		} catch (Exception $e) {
			$json['Success'] = false;
			$json['Result'] = $e->getMessage();
			return json_encode($json);
		}
	}


    public function CompleteDispositionOverrideBulk ($data) {  
		if(!isset($_SESSION['user'])) {
			$json['Success'] = false;
			$json['Result'] = 'Login to continue';
			return json_encode($json);
		}
		$json = array(
			'Success' => false,
			'Result' => $data
		);
        //return json_encode($json);
		try {
			if(! $this->isPermitted($_SESSION['user']['ProfileID'],'Disposition Override Bulk')) {
				$json['Success'] = false;
				$json['Result'] = 'No Access to Disposition Override Bulk Page';
				return json_encode($json);
			}

            if(! $this->isWritePermitted($_SESSION['user']['ProfileID'],'Disposition Override Bulk')) {
                $json['Success'] = false;
                $json['Result'] = 'You have Read only Access to Disposition Override Bulk Page';
                return json_encode($json);
            }

            $event_id = rand(1000000000, 9999999999);
            //Start validate TOBIN
            $query = "select c.*,d.disposition from custompallet c left join disposition d on c.disposition_id = d.disposition_id where c.BinName = '".mysqli_real_escape_string($this->connectionlink,$data['BinName'])."'";
            $q = mysqli_query($this->connectionlink,$query);
            if(mysqli_error($this->connectionlink)) {			
                $json['Success'] = false;
                $json['Result'] = mysqli_error($this->connectionlink);
                return json_encode($json);
            }			
            if(mysqli_affected_rows($this->connectionlink) > 0) {
                $to_cp = mysqli_fetch_assoc($q);
                if($to_cp['InventoryBased'] == '1') {
                    $json['Success'] = false;
                    $json['Result'] = 'To BIN is dedicated for Sub Component';
                    return json_encode($json);
                }
                if($to_cp['StatusID'] != '1') {				
                    $json['Success'] = false;
                    $json['Result'] = 'To BIN Status is not Active';
                    return json_encode($json);
                }

                if($to_cp['FacilityID'] != $_SESSION['user']['FacilityID']) {									
                    $json['Success'] = false;
                    $json['Result'] = 'To BIN Facility is different from Users Facility';
                    return json_encode($json);
                }
                // Allow bins with AssetsCount = 0 and disposition_id = null
                if($to_cp['disposition_id'] != $data['ToDispositionID']) {
                    // Check if bin is empty and has no disposition (null)
                    if($to_cp['AssetsCount'] == 0 && ($to_cp['disposition_id'] == null || $to_cp['disposition_id'] == '' || $to_cp['disposition_id'] == '0')) {
                        // Empty bin with no disposition - allow and mark for disposition update
                        $to_cp['RequiresDispositionUpdate'] = true;
                    } else {
                        $json['Success'] = false;
                        $json['Result'] = 'To BIN disposition is different from Disposition To';
                        return json_encode($json);
                    }
                }
            } else {
                $json['Success'] = false;
                $json['Result'] = 'Invalid To BIN Name';
                return json_encode($json);
            }
            //End validate TOBIN


            //Start validate FROM BIN
            //$query2 = "select * from custompallet where BinName = '".mysqli_real_escape_string($this->connectionlink,$data['FromBinName'])."'";
            $query2 = "select c.*,d.disposition from custompallet c left join disposition d on c.disposition_id = d.disposition_id where c.BinName = '".mysqli_real_escape_string($this->connectionlink,$data['FromBinName'])."'";
            $q2 = mysqli_query($this->connectionlink,$query2);
            if(mysqli_error($this->connectionlink)) {			
                $json['Success'] = false;
                $json['Result'] = mysqli_error($this->connectionlink);
                return json_encode($json);
            }			
            if(mysqli_affected_rows($this->connectionlink) > 0) {
                $from_cp = mysqli_fetch_assoc($q2);
                if($from_cp['InventoryBased'] == '1') {
                    $json['Success'] = false;
                    $json['Result'] = 'From BIN is dedicated for Sub Component';
                    return json_encode($json);
                }
                if($from_cp['StatusID'] != '1') {				
                    $json['Success'] = false;
                    $json['Result'] = 'From BIN Status is not Active';
                    return json_encode($json);
                }

                if($from_cp['FacilityID'] != $_SESSION['user']['FacilityID']) {									
                    $json['Success'] = false;
                    $json['Result'] = 'From BIN Facility is different from Users Facility';
                    return json_encode($json);
                }                
            } else {
                $json['Success'] = false;
                $json['Result'] = 'Invalid From BIN Name';
                return json_encode($json);
            }
            //End validate FROM BIN
 
 
            if($from_cp['CustomPalletID'] == $to_cp['CustomPalletID']) {
                $json['Success'] = false;
                $json['Result'] = "From and To Bins can't be same";
                return json_encode($json);
            }

            // Add Max Limit validation (only if MaxLimitRequired = 1)
            if($to_cp['MaxLimitRequired'] == '1') {
                // Get count of assets that will be moved from From Bin
                $fromAssetsQuery = "SELECT COUNT(*) as asset_count FROM asset WHERE CustomPalletID = '".mysqli_real_escape_string($this->connectionlink,$from_cp['CustomPalletID'])."'";
                $fromAssetsResult = mysqli_query($this->connectionlink, $fromAssetsQuery);
                if(mysqli_error($this->connectionlink)) {
                    $json['Success'] = false;
                    $json['Result'] = mysqli_error($this->connectionlink);
                    return json_encode($json);
                }

                $fromAssetsRow = mysqli_fetch_assoc($fromAssetsResult);
                $assetsToMove = intval($fromAssetsRow['asset_count']);
                $currentAssetsInToBin = intval($to_cp['AssetsCount']);
                $maxAssetsAllowed = intval($to_cp['MaximumAssets']);

                // Check if destination bin has enough space
                if(($currentAssetsInToBin + $assetsToMove) > $maxAssetsAllowed) {
                    $availableSpace = $maxAssetsAllowed - $currentAssetsInToBin;
                    $json['Success'] = false;
                    $json['Result'] = "Destination bin does not have enough space. Available space: $availableSpace, Assets to move: $assetsToMove. Maximum capacity: $maxAssetsAllowed";
                    return json_encode($json);
                }
            }

            // Customer Lock Validation
            if($to_cp['CustomerLock'] == '1') {
                // Get all unique customers from the From Bin assets
                $fromCustomerQuery = "
                    SELECT DISTINCT AWSCustomerID FROM (
                        SELECT AWSCustomerID FROM asset WHERE CustomPalletID = '".mysqli_real_escape_string($this->connectionlink,$from_cp['CustomPalletID'])."' AND AWSCustomerID IS NOT NULL AND AWSCustomerID != '' AND AWSCustomerID != '0'
                        UNION
                        SELECT AWSCustomerID FROM speed_server_recovery WHERE CustomPalletID = '".mysqli_real_escape_string($this->connectionlink,$from_cp['CustomPalletID'])."' AND AWSCustomerID IS NOT NULL AND AWSCustomerID != '' AND AWSCustomerID != '0'
                        UNION
                        SELECT AWSCustomerID FROM speed_media_recovery WHERE CustomPalletID = '".mysqli_real_escape_string($this->connectionlink,$from_cp['CustomPalletID'])."' AND AWSCustomerID IS NOT NULL AND AWSCustomerID != '' AND AWSCustomerID != '0'
                    ) AS combined_customers
                ";
                $fromCustomerResult = mysqli_query($this->connectionlink, $fromCustomerQuery);
                if(mysqli_error($this->connectionlink)) {
                    $json['Success'] = false;
                    $json['Result'] = mysqli_error($this->connectionlink);
                    return json_encode($json);
                }

                $fromCustomers = array();
                while($custRow = mysqli_fetch_assoc($fromCustomerResult)) {
                    $fromCustomers[] = $custRow['AWSCustomerID'];
                }

                // Check if all assets in From Bin belong to the same customer
                if(count($fromCustomers) > 1) {
                    // Get customer names for error message
                    $customerNames = array();
                    foreach($fromCustomers as $custId) {
                        $custQuery = "SELECT Customer FROM aws_customers WHERE AWSCustomerID = '".mysqli_real_escape_string($this->connectionlink,$custId)."'";
                        $custResult = mysqli_query($this->connectionlink, $custQuery);
                        if(mysqli_affected_rows($this->connectionlink) > 0) {
                            $custRow = mysqli_fetch_assoc($custResult);
                            $customerNames[] = $custRow['Customer'];
                        }
                    }
                    $json['Success'] = false;
                    $json['Result'] = 'From Bin contains assets from multiple customers (' . implode(', ', $customerNames) . '). Cannot move to customer-locked bin.';
                    return json_encode($json);
                }

                // Get destination bin's customer
                $destinationCustomerID = $to_cp['AWSCustomerID'];

                // Check if destination bin is empty (no customer assigned yet)
                if($destinationCustomerID == NULL || $destinationCustomerID == '' || $destinationCustomerID == '0') {
                    if(count($fromCustomers) == 1) {
                        // Destination bin is empty, assign it to the From Bin's customer
                        $fromCustomerID = $fromCustomers[0];
                        $custQuery = "SELECT Customer FROM aws_customers WHERE AWSCustomerID = '".mysqli_real_escape_string($this->connectionlink,$fromCustomerID)."'";
                        $custResult = mysqli_query($this->connectionlink, $custQuery);
                        $customerName = 'Unknown Customer';
                        if(mysqli_affected_rows($this->connectionlink) > 0) {
                            $custRow = mysqli_fetch_assoc($custResult);
                            $customerName = $custRow['Customer'];
                        }

                        // Update destination bin's customer
                        $updateBinQuery = "UPDATE custompallet SET AWSCustomerID = '".mysqli_real_escape_string($this->connectionlink,$fromCustomerID)."' WHERE CustomPalletID = '".mysqli_real_escape_string($this->connectionlink,$to_cp['CustomPalletID'])."'";
                        $updateBinResult = mysqli_query($this->connectionlink, $updateBinQuery);
                        if(mysqli_error($this->connectionlink)) {
                            $json['Success'] = false;
                            $json['Result'] = mysqli_error($this->connectionlink);
                            return json_encode($json);
                        }

                        // Add tracking record
                        $trackingAction = "Bin customer assigned to " . $customerName . " during bulk disposition override from " . $from_cp['BinName'];
                        $trackingQuery = "INSERT INTO custompallet_tracking (CustomPalletID, BinName, Action, CreatedDate, CreatedBy) VALUES ('".mysqli_real_escape_string($this->connectionlink,$to_cp['CustomPalletID'])."', '".mysqli_real_escape_string($this->connectionlink,$to_cp['BinName'])."', '".mysqli_real_escape_string($this->connectionlink,$trackingAction)."', NOW(), '".$_SESSION['user']['UserId']."')";
                        mysqli_query($this->connectionlink, $trackingQuery);

                        // Update the to_cp array for later use
                        $to_cp['AWSCustomerID'] = $fromCustomerID;
                    }
                } else {
                    // Destination bin has a customer, validate From Bin assets match
                    if(count($fromCustomers) == 1 && $fromCustomers[0] != $destinationCustomerID) {
                        // Get customer names for error message
                        $destCustQuery = "SELECT Customer FROM aws_customers WHERE AWSCustomerID = '".mysqli_real_escape_string($this->connectionlink,$destinationCustomerID)."'";
                        $destCustResult = mysqli_query($this->connectionlink, $destCustQuery);
                        $destCustomerName = 'Unknown Customer';
                        if(mysqli_affected_rows($this->connectionlink) > 0) {
                            $destCustRow = mysqli_fetch_assoc($destCustResult);
                            $destCustomerName = $destCustRow['Customer'];
                        }

                        $fromCustQuery = "SELECT Customer FROM aws_customers WHERE AWSCustomerID = '".mysqli_real_escape_string($this->connectionlink,$fromCustomers[0])."'";
                        $fromCustResult = mysqli_query($this->connectionlink, $fromCustQuery);
                        $fromCustomerName = 'Unknown Customer';
                        if(mysqli_affected_rows($this->connectionlink) > 0) {
                            $fromCustRow = mysqli_fetch_assoc($fromCustResult);
                            $fromCustomerName = $fromCustRow['Customer'];
                        }

                        $json['Success'] = false;
                        $json['Result'] = 'Destination bin is locked to ' . $destCustomerName . ', but From Bin assets belong to ' . $fromCustomerName . '. Cannot move assets.';
                        return json_encode($json);
                    }
                }
            }

            //Start consolidating BINs

            if($data['ToDispositionID'] == '4' && ! $data['rule_id']) {
                $json['Success'] = false;
				$json['Result'] = 'Invalid Business Rule ID';
				return json_encode($json);
            }

            //Insert into Asset Tracking			
			$desc = "Asset Disposition (New Disposition : ".$to_cp['disposition'].") updated and moved to BIN, (BIN ID : ".$to_cp['BinName']."),(Requester Login : ".$data['RequesterLogin'].") in Disposition Override Bulk Screen";
			//$query3 = "insert into asset_tracking (AssetScanID,Action,Description,UniqueID,CreatedDate,CreatedBy) values ('".mysqli_real_escape_string($this->connectionlink,$asset['AssetScanID'])."','".mysqli_real_escape_string($this->connectionlink,$desc)."','','',NOW(),'".$_SESSION['user']['UserId']."')";
            $query3 = "insert into asset_tracking (AssetScanID,Action,Description,UniqueID,CreatedDate,CreatedBy) 
            select AssetScanID,'".mysqli_real_escape_string($this->connectionlink,$desc)."','','',NOW(),'".$_SESSION['user']['UserId']."' from custompallet_items where CustomPalletID = '".mysqli_real_escape_string($this->connectionlink,$from_cp['CustomPalletID'])."' and AssetScanID > 0";
			$q3 = mysqli_query($this->connectionlink,$query3);
			if(mysqli_error($this->connectionlink)) {
				$json['Success'] = false;
				$json['Result'] = mysqli_error($this->connectionlink);
				return json_encode($json);
			}
			if(mysqli_affected_rows($this->connectionlink) > 0) {

			} else {
				// $json['Success'] = false;
				// $json['Result'] = 'No Serials available in From BIN';
				// return json_encode($json);
			}
			//End Inserting into Asset Tracking


            //Start get Override reason deatails
            $query22 = "select ReasonID,OverrideReason from disposition_override_reason where ReasonID = '".mysqli_real_escape_string($this->connectionlink,$data['OverrideReason'])."'";
            $q22 = mysqli_query($this->connectionlink,$query22);
            if(mysqli_error($this->connectionlink)) {
                $json['Success'] = false;
                $json['Result'] = mysqli_error($this->connectionlink);
                return json_encode($json);
            }
            if(mysqli_affected_rows($this->connectionlink) > 0) {
                $row22 = mysqli_fetch_assoc($q22);
                $data['OverrideReason'] = $row22['OverrideReason'];
            } else {
                $json['Success'] = false;
                $json['Result'] = 'Invalid Override Reason';
                return json_encode($json);
            }
            //ENd get Override reason deails

            //Insert into Disposition Override table	
            
            $batch_event_flag = 'Y';            
            $query5 = "insert into disposition_override_assets (AssetScanID,SerialNumber,FromCustomPalletID,ToCustomPalletID,FromDispositionID,ToDispositionID,OverrideReason,Comments,CreatedDate,CreatedBy,RequesterLogin,Notes,TPVRController,OverrideType,notes_scan_time,event_id,batch_event_flag) 
            select AssetScanID,SerialNumber,CustomPalletID,'".$to_cp['CustomPalletID']."',disposition_id,'".$data['ToDispositionID']."','".$data['OverrideReason']."','Disposition Override Bulk',NOW(),'".$_SESSION['user']['UserId']."','".mysqli_real_escape_string($this->connectionlink,$data['RequesterLogin'])."','".mysqli_real_escape_string($this->connectionlink,$data['Notes'])."','".mysqli_real_escape_string($this->connectionlink,$data['AuditController'])."','Bin-Level','".mysqli_real_escape_string($this->connectionlink,$data['notes_scan_time'])."','".mysqli_real_escape_string($this->connectionlink,$event_id)."','".mysqli_real_escape_string($this->connectionlink,$batch_event_flag)."' from asset where CustomPalletID = '".mysqli_real_escape_string($this->connectionlink,$from_cp['CustomPalletID'])."' ";            
			$q5 = mysqli_query($this->connectionlink,$query5);
			if(mysqli_error($this->connectionlink)) {
				$json['Success'] = false;
				$json['Result'] = mysqli_error($this->connectionlink);
				return json_encode($json);
			}			
			//End Insert into Disposition Override table	



            //Start update Asset
			$query1 = "update asset set CustomPalletID = '".mysqli_real_escape_string($this->connectionlink,$to_cp['CustomPalletID'])."',disposition_id = '".mysqli_real_escape_string($this->connectionlink,$data['ToDispositionID'])."',DateUpdated = NOW(),UpdatedBy = '".$_SESSION['user']['UserId']."',DispositionOverrideReason = '".mysqli_real_escape_string($this->connectionlink,$data['OverrideReason'])."' ";
            if($data['ToDispositionID'] == '4') {
                $query1 = $query1. ",RecentDispositionDate = NOW(),RecentDispositionRuleID = '".mysqli_real_escape_string($this->connectionlink,$data['rule_id'])."',RecentDispositionBy = '".$_SESSION['user']['UserId']."',RecentDispositionComments = 'Updated in Disposition Override Bulk' ";
            }
            $query1 = $query1." where CustomPalletID = '".mysqli_real_escape_string($this->connectionlink,$from_cp['CustomPalletID'])."'";
			$q1 = mysqli_query($this->connectionlink,$query1);
			if(mysqli_error($this->connectionlink)) {
				$json['Success'] = false;
				$json['Result'] = mysqli_error($this->connectionlink);
				return json_encode($json);
			}			
			//End update Asset



            //Insert into server recovery tracking			
			$desc = "Serial Disposition (New Disposition : ".$to_cp['disposition'].") updated and moved to BIN, (BIN ID : ".$to_cp['BinName']."),(Requester Login : ".$data['RequesterLogin'].") in Disposition Override Bulk Screen";			
            $query3 = "insert into speed_server_recovery_tracking (ServerID,ServerSerialNumber,Type,idPallet,Action,Description,CreatedDate,CreatedBy) 
            select ServerID,ServerSerialNumber,Type,idPallet,'".mysqli_real_escape_string($this->connectionlink,$desc)."','',NOW(),'".$_SESSION['user']['UserId']."' from speed_server_recovery where CustomPalletID = '".mysqli_real_escape_string($this->connectionlink,$from_cp['CustomPalletID'])."'";
			$q3 = mysqli_query($this->connectionlink,$query3);
			if(mysqli_error($this->connectionlink)) {
				$json['Success'] = false;
				$json['Result'] = mysqli_error($this->connectionlink);
				return json_encode($json);
			}			
			//End Inserting into server recovery



            //Insert into Disposition Override table	                     
            $query5 = "insert into disposition_override_assets (ServerID,SerialNumber,FromCustomPalletID,ToCustomPalletID,FromDispositionID,ToDispositionID,OverrideReason,Comments,CreatedDate,CreatedBy,RequesterLogin,Notes,TPVRController,OverrideType,notes_scan_time,event_id,batch_event_flag) 
            select ServerID,ServerSerialNumber,CustomPalletID,'".$to_cp['CustomPalletID']."',disposition_id,'".$data['ToDispositionID']."','".$data['OverrideReason']."','Disposition Override Bulk',NOW(),'".$_SESSION['user']['UserId']."','".mysqli_real_escape_string($this->connectionlink,$data['RequesterLogin'])."','".mysqli_real_escape_string($this->connectionlink,$data['Notes'])."','".mysqli_real_escape_string($this->connectionlink,$data['AuditController'])."','Bin-Level','".mysqli_real_escape_string($this->connectionlink,$data['notes_scan_time'])."','".mysqli_real_escape_string($this->connectionlink,$event_id)."','".mysqli_real_escape_string($this->connectionlink,$batch_event_flag)."' from speed_server_recovery where CustomPalletID = '".mysqli_real_escape_string($this->connectionlink,$from_cp['CustomPalletID'])."' ";            
			$q5 = mysqli_query($this->connectionlink,$query5);
			if(mysqli_error($this->connectionlink)) {
				$json['Success'] = false;
				$json['Result'] = mysqli_error($this->connectionlink);
				return json_encode($json);
			}			
			//End Insert into Disposition Override table	


            //Start update speed_server_Recovery
			$query1 = "update speed_server_recovery set CustomPalletID = '".mysqli_real_escape_string($this->connectionlink,$to_cp['CustomPalletID'])."',disposition_id = '".mysqli_real_escape_string($this->connectionlink,$data['ToDispositionID'])."',UpdatedDate = NOW(),UpdatedBy = '".$_SESSION['user']['UserId']."',DispositionOverrideReason = '".mysqli_real_escape_string($this->connectionlink,$data['OverrideReason'])."' ";            
            $query1 = $query1." where CustomPalletID = '".mysqli_real_escape_string($this->connectionlink,$from_cp['CustomPalletID'])."'";
			$q1 = mysqli_query($this->connectionlink,$query1);
			if(mysqli_error($this->connectionlink)) {
				$json['Success'] = false;
				$json['Result'] = mysqli_error($this->connectionlink);
				return json_encode($json);
			}			
			//End update speed_server_Recovery





            //Insert into media recovery tracking			
			$desc = "Serial Disposition (New Disposition : ".$to_cp['disposition'].") updated and moved to BIN, (BIN ID : ".$to_cp['BinName']."),(Requester Login : ".$data['RequesterLogin'].") in Disposition Override Bulk Screen";			
            $query3 = "insert into speed_media_recovery_tracking (MediaID,MediaSerialNumber,ServerSerialNumber,MediaType,idPallet,Action,Description,CreatedDate,CreatedBy) 
            select MediaID,MediaSerialNumber,ServerSerialNumber,MediaType,idPallet,'".mysqli_real_escape_string($this->connectionlink,$desc)."','',NOW(),'".$_SESSION['user']['UserId']."' from speed_media_recovery where CustomPalletID = '".mysqli_real_escape_string($this->connectionlink,$from_cp['CustomPalletID'])."'";
			$q3 = mysqli_query($this->connectionlink,$query3);
			if(mysqli_error($this->connectionlink)) {
				$json['Success'] = false;
				$json['Result'] = mysqli_error($this->connectionlink);
				return json_encode($json);
			}			
			//End Inserting into media recovery



            //Insert into Disposition Override table	                     
            $query5 = "insert into disposition_override_assets (MediaID,SerialNumber,FromCustomPalletID,ToCustomPalletID,FromDispositionID,ToDispositionID,OverrideReason,Comments,CreatedDate,CreatedBy,RequesterLogin,Notes,TPVRController,OverrideType,notes_scan_time,event_id,batch_event_flag) 
            select MediaID,MediaSerialNumber,CustomPalletID,'".$to_cp['CustomPalletID']."',disposition_id,'".$data['ToDispositionID']."','".$data['OverrideReason']."','Disposition Override Bulk',NOW(),'".$_SESSION['user']['UserId']."','".mysqli_real_escape_string($this->connectionlink,$data['RequesterLogin'])."','".mysqli_real_escape_string($this->connectionlink,$data['Notes'])."','".mysqli_real_escape_string($this->connectionlink,$data['AuditController'])."','Bin-Level','".mysqli_real_escape_string($this->connectionlink,$data['notes_scan_time'])."','".mysqli_real_escape_string($this->connectionlink,$event_id)."','".mysqli_real_escape_string($this->connectionlink,$batch_event_flag)."' from speed_media_recovery where CustomPalletID = '".mysqli_real_escape_string($this->connectionlink,$from_cp['CustomPalletID'])."' ";            
			$q5 = mysqli_query($this->connectionlink,$query5);
			if(mysqli_error($this->connectionlink)) {
				$json['Success'] = false;
				$json['Result'] = mysqli_error($this->connectionlink);
				return json_encode($json);
			}			
			//End Insert into Disposition Override table	


            //Start update speed_server_Recovery
			$query1 = "update speed_media_recovery set CustomPalletID = '".mysqli_real_escape_string($this->connectionlink,$to_cp['CustomPalletID'])."',disposition_id = '".mysqli_real_escape_string($this->connectionlink,$data['ToDispositionID'])."',UpdatedDate = NOW(),UpdatedBy = '".$_SESSION['user']['UserId']."',DispositionOverrideReason = '".mysqli_real_escape_string($this->connectionlink,$data['OverrideReason'])."' ";            
            $query1 = $query1." where CustomPalletID = '".mysqli_real_escape_string($this->connectionlink,$from_cp['CustomPalletID'])."'";
			$q1 = mysqli_query($this->connectionlink,$query1);
			if(mysqli_error($this->connectionlink)) {
				$json['Success'] = false;
				$json['Result'] = mysqli_error($this->connectionlink);
				return json_encode($json);
			}			
			//End update speed_server_Recovery



			//Start update Custom Pallet Items
			$query2 = "update custompallet_items set CustomPalletID = '".mysqli_real_escape_string($this->connectionlink,$to_cp['CustomPalletID'])."' where CustomPalletID = '".mysqli_real_escape_string($this->connectionlink,$from_cp['CustomPalletID'])."'";
			$q2 = mysqli_query($this->connectionlink,$query2);
			if(mysqli_error($this->connectionlink)) {
				$json['Success'] = false;
				$json['Result'] = mysqli_error($this->connectionlink);
				return json_encode($json);
			}
			//End update Custom Pallet Items

			//Start update Custom Pallet Counts
            $query33 = "select count(*) from custompallet_items where CustomPalletID = '".mysqli_real_escape_string($this->connectionlink,$to_cp['CustomPalletID'])."' ";
            $q33 = mysqli_query($this->connectionlink,$query33);
			if(mysqli_error($this->connectionlink)) {
				$json['Success'] = false;
				$json['Result'] = mysqli_error($this->connectionlink);
				return json_encode($json);
			}
            if(mysqli_affected_rows($this->connectionlink) > 0) {
                $row33 = mysqli_fetch_assoc($q33);
                $query3 = "UPDATE `custompallet` SET `AssetsCount`= '".mysqli_real_escape_string($this->connectionlink,$row33['count(*)'])."' WHERE `CustomPalletID`='".mysqli_real_escape_string($this->connectionlink,$to_cp['CustomPalletID'])."'";
                $q3 = mysqli_query($this->connectionlink,$query3);
                if(mysqli_error($this->connectionlink)) {
                    $json['Success'] = false;
                    $json['Result'] = mysqli_error($this->connectionlink);
                    return json_encode($json);
                }
            }			

			$query4 = "UPDATE `custompallet` SET `AssetsCount`= 0 WHERE `CustomPalletID`='".mysqli_real_escape_string($this->connectionlink,$from_cp['CustomPalletID'])."'";
			$q4 = mysqli_query($this->connectionlink,$query4);
			if(mysqli_error($this->connectionlink)) {
				$json['Success'] = false;
				$json['Result'] = mysqli_error($this->connectionlink);
				return json_encode($json);
			}
			//End update Custom Pallet Counts

            //End consolidating BINs

            // Check if bin needs disposition update (empty bin with no disposition that now has assets)
            if($to_cp['AssetsCount'] == 0 && ($to_cp['disposition_id'] == null || $to_cp['disposition_id'] == '' || $to_cp['disposition_id'] == '0')) {
                // Get the disposition name for tracking
                $dispQuery = "SELECT disposition FROM disposition WHERE disposition_id = '".mysqli_real_escape_string($this->connectionlink,$data['ToDispositionID'])."'";
                $dispResult = mysqli_query($this->connectionlink, $dispQuery);
                $dispositionName = 'Unknown Disposition';
                if(mysqli_affected_rows($this->connectionlink) > 0) {
                    $dispRow = mysqli_fetch_assoc($dispResult);
                    $dispositionName = $dispRow['disposition'];
                }

                // Update bin's disposition to match the assets' disposition
                $updateBinDispQuery = "UPDATE custompallet SET disposition_id = '".mysqli_real_escape_string($this->connectionlink,$data['ToDispositionID'])."' WHERE CustomPalletID = '".mysqli_real_escape_string($this->connectionlink,$to_cp['CustomPalletID'])."'";
                $updateBinDispResult = mysqli_query($this->connectionlink, $updateBinDispQuery);
                if(mysqli_error($this->connectionlink)) {
                    $json['Success'] = false;
                    $json['Result'] = mysqli_error($this->connectionlink);
                    return json_encode($json);
                }

                // Add tracking record for disposition update
                $trackingAction = "Bin disposition updated to " . $dispositionName . " after bulk disposition override from " . $from_cp['BinName'];
                $trackingQuery = "INSERT INTO custompallet_tracking (CustomPalletID, BinName, Action, CreatedDate, CreatedBy) VALUES ('".mysqli_real_escape_string($this->connectionlink,$to_cp['CustomPalletID'])."', '".mysqli_real_escape_string($this->connectionlink,$to_cp['BinName'])."', '".mysqli_real_escape_string($this->connectionlink,$trackingAction)."', NOW(), '".$_SESSION['user']['UserId']."')";
                mysqli_query($this->connectionlink, $trackingQuery);
            }

            $this->ValidateUserInputNotes($data['Notes']);
            $json['Success'] = true;
            $json['Result'] = 'You have successfully overrode from '.$data['FromBinName'].' to '.$data['BinName'].'. All Records can be found in Workflow Search';
            return json_encode($json);
		} catch (Exception $e) {
			$json['Success'] = false;
			$json['Result'] = $e->getMessage();
			return json_encode($json);
		}
	}


    public function ValidateShippingContainerDisposition ($data) {
		if(!isset($_SESSION['user'])) {
			$json['Success'] = false;
			$json['Result'] = 'Login to continue';
			return json_encode($json);
		}
		$json = array(
			'Success' => false,
			'Result' => 'No Data'
		);	
		try {
            $query = "select d.*,l.LocationName,g.GroupName,g.GroupName as `group` from shipping_containers d 
            LEFT join location l on d.LocationID = l.LocationID 
			LEFT JOIN location_group g on l.GroupID = g.GroupID 
            where d.ShippingContainerID = '".mysqli_real_escape_string($this->connectionlink,$data['ShippingContainerID'])."'";
            $q = mysqli_query($this->connectionlink,$query);
            if(mysqli_error($this->connectionlink)) {
                $json['Success'] = false;
                $json['Result'] = mysqli_error($this->connectionlink);
                return json_encode($json);
            } else {
                if(mysqli_affected_rows($this->connectionlink) > 0) {
                    $row = mysqli_fetch_assoc($q);
                    if($row['ShippingID'] != '') {
                        $json['Success'] = false;
                        $json['Result'] = 'Container is added to Shipment '.$row['ShippingID'];
                        return json_encode($json);
                    }
                   
                    // if($row['StatusID'] != '1' && $row['StatusID'] != '6') {
                    //     $json['Success'] = false;
                    //     $json['Result'] = 'Container Status is not Active';
                    //     return json_encode($json);
                    // }

                    if($row['StatusID'] != '6') {
                        $json['Success'] = false;
                        $json['Result'] = 'Only Closed Containers are allowed to Disposition Override';
                        return json_encode($json);
                    }
    
                    if($row['FacilityID'] != $_SESSION['user']['FacilityID']) {									
                        $json['Success'] = false;
                        $json['Result'] = 'Container Facility is different from Users Facility';
                        return json_encode($json);
                    }
                    if($row['disposition_id'] != $data['DispositionFrom']) {
                        $json['Success'] = false;
                        $json['Result'] = "Container Removal Type is different from 'Disposition From'";
                        return json_encode($json);
                    }

                    //Start check no. of items from the Container
                    $query6 = "select count(*) from shipping_container_serials where ShippingContainerID = '".mysqli_real_escape_string($this->connectionlink,$data['ShippingContainerID'])."'";
                    $q6 = mysqli_query($this->connectionlink,$query6);
                    if(mysqli_error($this->connectionlink)) {
                        $json['Success'] = false;
                        $json['Result'] = mysqli_error($this->connectionlink);
                        return json_encode($json);
                    }
                    if(mysqli_affected_rows($this->connectionlink) > 0) {
                        $row6 = mysqli_fetch_assoc($q6);
                        if($row6['count(*)'] == 0) {
                            $json['Success'] = false;
                            $json['Result'] = "At least one serial is required for Disposition Override, as the container currently has none.";
                            return json_encode($json);
                        }
                        $row['ContainerAssetQuantity'] = $row6['count(*)'];
                    } else {
                        $json['Success'] = false;
                        $json['Result'] = 'Invalid Container ID';
                        return json_encode($json);
                    }
                    //End check no. of items from the Container

                    $json['Success'] = true;
                    $json['Result'] = $row;
                    return json_encode($json);

                } else {
                    $json['Success'] = false;
                    $json['Result'] = 'Invalid Container ID';
                    return json_encode($json);
                }
            }
            return json_encode($json);
            
		} catch (Exception $e) {
			$json['Success'] = false;
			$json['Result'] = $e->getMessage();
			return json_encode($json);
		}
	}


    public function CompleteDispositionOverrideContainer ($data) {
		if(!isset($_SESSION['user'])) {
			$json['Success'] = false;
			$json['Result'] = 'Login to continue';
			return json_encode($json);
		}
		$json = array(
			'Success' => false,
			'Result' => $data
		);
        //return json_encode($json);
		try {
			if(! $this->isPermitted($_SESSION['user']['ProfileID'],'Disposition Override Container')) {
				$json['Success'] = false;
				$json['Result'] = 'No Access to Disposition Override Container Page';
				return json_encode($json);
			}

            if(! $this->isWritePermitted($_SESSION['user']['ProfileID'],'Disposition Override Container')) {
                $json['Success'] = false;
                $json['Result'] = 'You have Read only Access to Disposition Override Container Page';
                return json_encode($json);
            }
            $event_id = rand(1000000000, 9999999999);
            $query16 = "select * from disposition where disposition_id = '".mysqli_real_escape_string($this->connectionlink,$data['ToDispositionID'])."'";
            $q16 = mysqli_query($this->connectionlink,$query16);
            if(mysqli_error($this->connectionlink)) {			
                $json['Success'] = false;			
                $json['Result'] = mysqli_error($this->connectionlink);			
                return json_encode($json);			
            }
            if(mysqli_affected_rows($this->connectionlink) > 0) {
                $to_disposition = mysqli_fetch_assoc($q16);                
            } else {
                $json['Success'] = false;			
                $json['Result'] = 'Invalid to disposition';			
                return json_encode($json);			
            }


            //Start get Container Details
            $query = "select * from shipping_containers where ShippingContainerID = '".mysqli_real_escape_string($this->connectionlink,$data['ShippingContainerID'])."'";
            $q = mysqli_query($this->connectionlink,$query);
            if(mysqli_error($this->connectionlink)) {			
                $json['Success'] = false;			
                $json['Result'] = mysqli_error($this->connectionlink);			
                return json_encode($json);			
            }
            if(mysqli_affected_rows($this->connectionlink) > 0) {
                $row = mysqli_fetch_assoc($q);
                if($row['StatusID'] != '1' && $row['StatusID'] != '7' && $row['StatusID'] != '6') {
                //if($row['StatusID'] != '1') {
                    $json['Success'] = false;			
                    $json['Result'] = 'Container Status is not Active';
                    return json_encode($json);
                }					
            }
            if($row['SealID'] != $data['SealID']) {
                $seal_changed = '1';
            } else {
                $seal_changed = '0';
            }
            ///End get Container Details

            //Start check for min weight
            $query4 = "select packageWeight,OptionalLocation from package where idPackage = '".mysqli_real_escape_string($this->connectionlink,$row['idPackage'])."' ";
            $q4 = mysqli_query($this->connectionlink,$query4);
            if(mysqli_error($this->connectionlink)) {			
                $json['Success'] = false;			
                $json['Result'] = mysqli_error($this->connectionlink);			
                return json_encode($json);			
            }
            if(mysqli_affected_rows($this->connectionlink) > 0) {
                $row4 = mysqli_fetch_assoc($q4);
                // if($row4['packageWeight'] >= $data['ContainerWeight']) {
                // 	$json['Success'] = false;			
                // 	$json['Result'] = 'Container weight should be greater than Container Type weight ('.$row4['packageWeight'].')';			
                // 	return json_encode($json);
                // }
            } else {
                $json['Success'] = false;			
                $json['Result'] = 'Invalid Container Type';			
                return json_encode($json);
            }
            //End check for min weight

            if($data['group'] != $data['GroupName']) { // Location Changed
                if($data['group'] != '' && $data['group'] != null && $data['group'] != 'undefined') {

                    //Start check if valid Group
                    $query10 = "select GroupID,FacilityID,LocationType,BinTypeID from location_group where GroupName = '".mysqli_real_escape_string($this->connectionlink,$data['group'])."'";
                    $q10 = mysqli_query($this->connectionlink,$query10);
                    if(mysqli_error($this->connectionlink)) {
                        $json['Success'] = false;
                        $json['Result'] = mysqli_error($this->connectionlink);
                        return json_encode($json);
                    }
                    if(mysqli_affected_rows($this->connectionlink) > 0) {
                        $row10 = mysqli_fetch_assoc($q10);						
                        if($row10['FacilityID'] != $_SESSION['user']['FacilityID']) {
                            $json['Success'] = false;
                            $json['Result'] = 'Location Facility is different from User Facility';
                            return json_encode($json);
                        }
                        if($row10['LocationType'] != 'Outbound Storage') {
                            $json['Success'] = false;
                            $json['Result'] = 'Location is not Outbound Storage Location';
                            return json_encode($json);
                        }				
                        $GroupID = $row10['GroupID'];

                        //Start get free location from group selected
                        $query112 = "select LocationID,LocationType,LocationName from location where Locked = '2' and LocationStatus = '1' and GroupID = '".mysqli_real_escape_string($this->connectionlink,$GroupID)."'";
                        $q112 = mysqli_query($this->connectionlink,$query112);
                        if(mysqli_error($this->connectionlink)) {
                            $json['Success'] = false;
                            $json['Result'] = mysqli_error($this->connectionlink);
                            return json_encode($json);
                        }
                        if(mysqli_affected_rows($this->connectionlink) > 0) {
                            $row112 = mysqli_fetch_assoc($q112);
                            $data['NewLocationID'] = $row112['LocationID'];								
                            $newLocationName = $row112['LocationName'];
                            $data['LocationID'] = $row112['LocationID'];
                            $location_changed = true;
                        } else {
                            $json['Success'] = false;
                            $json['Result'] = 'No locations available, in selected group';
                            return json_encode($json);
                        }
                        //End get free location from group selected	

                    } else {
                        $json['Success'] = false;
                        $json['Result'] = 'Invalid Location Group';
                        return json_encode($json);
                    }
                    //End check if valid Group								
                } else {
                    $data['LocationID'] = '';
                    if($row4['OptionalLocation'] == 0) {
                        $json['Success'] = false;
                        $json['Result'] = 'Invalid Location';
                        return json_encode($json);
                    }
                    if($row['LocationID'] > 0) {
                        $location_changed = true;
                    } else {
                        $location_changed = false;
                    }
                }
            } else {
                $location_changed = false;
            }



            $query1 = "update shipping_containers set disposition_id = '".mysqli_real_escape_string($this->connectionlink,$data['ToDispositionID'])."',UpdatedDate = NOW(),UpdatedBy = '".$_SESSION['user']['UserId']."',SealID = '".mysqli_real_escape_string($this->connectionlink,$data['SealID'])."'";
            if($data['LocationID'] > 0) {
                $query1 = $query1.",LocationID = '".mysqli_real_escape_string($this->connectionlink,$data['LocationID'])."'";
            } else {
                $query1 = $query1.",LocationID = NULL";
            }            
            if($seal_changed == 1) {
                $query1 = $query1 . ",RecentSealDate = NOW(),RecentSealBy = '".$_SESSION['user']['UserId']."' ";
            }
            $query1 = $query1." where ShippingContainerID = '".mysqli_real_escape_string($this->connectionlink,$data['ShippingContainerID'])."'";
            $q1 = mysqli_query($this->connectionlink,$query1);				
            if(mysqli_error($this->connectionlink)) {			
                $json['Success'] = false;			
                $json['Result'] = mysqli_error($this->connectionlink);			
                return json_encode($json);			
            }
            

            //Start update the dispositions of all serials
            $query11 = "update asset set disposition_id = '".mysqli_real_escape_string($this->connectionlink,$data['ToDispositionID'])."' where ShippingContainerID = '".mysqli_real_escape_string($this->connectionlink,$data['ShippingContainerID'])."'";
            $q11 = mysqli_query($this->connectionlink,$query11);				
            if(mysqli_error($this->connectionlink)) {			
                $json['Success'] = false;			
                $json['Result'] = mysqli_error($this->connectionlink);			
                return json_encode($json);			
            }

            $query11 = "update inventory set disposition_id = '".mysqli_real_escape_string($this->connectionlink,$data['ToDispositionID'])."' where ShippingContainerID = '".mysqli_real_escape_string($this->connectionlink,$data['ShippingContainerID'])."'";
            $q11 = mysqli_query($this->connectionlink,$query11);				
            if(mysqli_error($this->connectionlink)) {			
                $json['Success'] = false;			
                $json['Result'] = mysqli_error($this->connectionlink);			
                return json_encode($json);			
            }

            $query11 = "update speed_server_recovery set disposition_id = '".mysqli_real_escape_string($this->connectionlink,$data['ToDispositionID'])."' where ShippingContainerID = '".mysqli_real_escape_string($this->connectionlink,$data['ShippingContainerID'])."'";
            $q11 = mysqli_query($this->connectionlink,$query11);				
            if(mysqli_error($this->connectionlink)) {			
                $json['Success'] = false;			
                $json['Result'] = mysqli_error($this->connectionlink);			
                return json_encode($json);			
            }

            $query11 = "update speed_media_recovery set disposition_id = '".mysqli_real_escape_string($this->connectionlink,$data['ToDispositionID'])."' where ShippingContainerID = '".mysqli_real_escape_string($this->connectionlink,$data['ShippingContainerID'])."'";
            $q11 = mysqli_query($this->connectionlink,$query11);				
            if(mysqli_error($this->connectionlink)) {			
                $json['Success'] = false;			
                $json['Result'] = mysqli_error($this->connectionlink);			
                return json_encode($json);			
            }
            //End update the dispositions of all serials

            

            //Insert into Asset Tracking			
			$desc = "Asset Disposition (New Disposition : ".$to_disposition['disposition'].") updated ,(Requester Login : ".$data['RequesterLogin'].") in Disposition Override Container Screen";
			//$query3 = "insert into asset_tracking (AssetScanID,Action,Description,UniqueID,CreatedDate,CreatedBy) values ('".mysqli_real_escape_string($this->connectionlink,$asset['AssetScanID'])."','".mysqli_real_escape_string($this->connectionlink,$desc)."','','',NOW(),'".$_SESSION['user']['UserId']."')";
            $query3 = "insert into asset_tracking (AssetScanID,Action,Description,UniqueID,CreatedDate,CreatedBy) 
            select AssetScanID,'".mysqli_real_escape_string($this->connectionlink,$desc)."','','',NOW(),'".$_SESSION['user']['UserId']."' from shipping_container_serials where ShippingContainerID = '".mysqli_real_escape_string($this->connectionlink,$data['ShippingContainerID'])."' and AssetScanID > 0 ";
			$q3 = mysqli_query($this->connectionlink,$query3);
			if(mysqli_error($this->connectionlink)) {
				$json['Success'] = false;
				$json['Result'] = mysqli_error($this->connectionlink);
				return json_encode($json);
			}
            
            
            $desc = "Serial Disposition (New Disposition : ".$to_disposition['disposition'].") updated ,(Requester Login : ".$data['RequesterLogin'].") in Disposition Override Container Screen";
			//$query3 = "insert into asset_tracking (AssetScanID,Action,Description,UniqueID,CreatedDate,CreatedBy) values ('".mysqli_real_escape_string($this->connectionlink,$asset['AssetScanID'])."','".mysqli_real_escape_string($this->connectionlink,$desc)."','','',NOW(),'".$_SESSION['user']['UserId']."')";
            $query3 = "insert into speed_server_recovery_tracking (ServerID,ServerSerialNumber,Type,Action,Description,CreatedDate,CreatedBy) 
            select ServerID,ServerSerialNumber,part_type,'".mysqli_real_escape_string($this->connectionlink,$desc)."','',NOW(),'".$_SESSION['user']['UserId']."' from shipping_container_serials where ShippingContainerID = '".mysqli_real_escape_string($this->connectionlink,$data['ShippingContainerID'])."' and ServerID > 0";
			$q3 = mysqli_query($this->connectionlink,$query3);
			if(mysqli_error($this->connectionlink)) {
				$json['Success'] = false;
				$json['Result'] = mysqli_error($this->connectionlink);
				return json_encode($json);
			}


            $desc = "Media Disposition (New Disposition : ".$to_disposition['disposition'].") updated ,(Requester Login : ".$data['RequesterLogin'].") in Disposition Override Container Screen";
			//$query3 = "insert into asset_tracking (AssetScanID,Action,Description,UniqueID,CreatedDate,CreatedBy) values ('".mysqli_real_escape_string($this->connectionlink,$asset['AssetScanID'])."','".mysqli_real_escape_string($this->connectionlink,$desc)."','','',NOW(),'".$_SESSION['user']['UserId']."')";
            $query3 = "insert into speed_media_recovery_tracking (MediaID,MediaSerialNumber,MediaType,Action,Description,CreatedDate,CreatedBy) 
            select MediaID,MediaSerialNumber,part_type,'".mysqli_real_escape_string($this->connectionlink,$desc)."','',NOW(),'".$_SESSION['user']['UserId']."' from shipping_container_serials where ShippingContainerID = '".mysqli_real_escape_string($this->connectionlink,$data['ShippingContainerID'])."' and MediaID > 0";
			$q3 = mysqli_query($this->connectionlink,$query3);
			if(mysqli_error($this->connectionlink)) {
				$json['Success'] = false;
				$json['Result'] = mysqli_error($this->connectionlink);
				return json_encode($json);
			}
			//End Inserting into Asset Tracking


            //Start get Override reason deatails
            $query22 = "select ReasonID,OverrideReason from disposition_override_reason where ReasonID = '".mysqli_real_escape_string($this->connectionlink,$data['OverrideReason'])."'";
            $q22 = mysqli_query($this->connectionlink,$query22);
            if(mysqli_error($this->connectionlink)) {
                $json['Success'] = false;
                $json['Result'] = mysqli_error($this->connectionlink);
                return json_encode($json);
            }
            if(mysqli_affected_rows($this->connectionlink) > 0) {
                $row22 = mysqli_fetch_assoc($q22);
                $data['OverrideReason'] = $row22['OverrideReason'];
            } else {
                $json['Success'] = false;
                $json['Result'] = 'Invalid Override Reason';
                return json_encode($json);
            }
            //ENd get Override reason deails

            //Insert into Disposition Override table			
            $query5 = "insert into disposition_override_assets (ShippingContainerID,FromDispositionID,ToDispositionID,OverrideReason,Comments,CreatedDate,CreatedBy,RequesterLogin,Notes,TPVRController,OverrideType,notes_scan_time,event_id,batch_event_flag) values(
                '".mysqli_real_escape_string($this->connectionlink,$data['ShippingContainerID'])."','".mysqli_real_escape_string($this->connectionlink,$data['FromDispositionID'])."','".mysqli_real_escape_string($this->connectionlink,$data['ToDispositionID'])."','".mysqli_real_escape_string($this->connectionlink,$data['OverrideReason'])."','Disposition Override Container',NOW(),'".$_SESSION['user']['UserId']."','".mysqli_real_escape_string($this->connectionlink,$data['RequesterLogin'])."','".mysqli_real_escape_string($this->connectionlink,$data['Notes'])."','".mysqli_real_escape_string($this->connectionlink,$data['AuditController'])."','Container-Level','".mysqli_real_escape_string($this->connectionlink,$data['notes_scan_time'])."','".mysqli_real_escape_string($this->connectionlink,$event_id)."','N'
            )";
            //select AssetScanID,SerialNumber,CustomPalletID,'".$to_cp['CustomPalletID']."',disposition_id,'".$data['ToDispositionID']."','".$data['OverrideReason']."','Disposition Override Bulk',NOW(),'".$_SESSION['user']['UserId']."','".mysqli_real_escape_string($this->connectionlink,$data['RequesterLogin'])."','".mysqli_real_escape_string($this->connectionlink,$data['Notes'])."','".mysqli_real_escape_string($this->connectionlink,$data['AuditController'])."','Container-Level' from asset where CustomPalletID = '".mysqli_real_escape_string($this->connectionlink,$from_cp['CustomPalletID'])."' ";            
			$q5 = mysqli_query($this->connectionlink,$query5);
			if(mysqli_error($this->connectionlink)) {
				$json['Success'] = false;
				$json['Result'] = mysqli_error($this->connectionlink);
				return json_encode($json);
			}			
			//End Insert into Disposition Override table	


            //End consolidating BINs
            $this->ValidateUserInputNotes($data['Notes']);
            $json['Success'] = true;
            $json['Result'] = 'You have successfully overrode the disposition of the Container';
            return json_encode($json);            
		} catch (Exception $e) {
			$json['Success'] = false;
			$json['Result'] = $e->getMessage();
			return json_encode($json);
		}
	}



    public function ValidateContainerSeal ($data) {
		if(!isset($_SESSION['user'])) {
			$json['Success'] = false;
			$json['Result'] = 'Login to continue';
			return json_encode($json);
		}
		$json = array(
			'Success' => false,
			'Result' => 'No Data'
		);	
		try {
			if(! $this->isPermitted($_SESSION['user']['ProfileID'],'Disposition Override Container')) {
				$json['Success'] = false;
				$json['Result'] = 'No Access to Disposition Override Container Page';
				return json_encode($json);
			}

            if(! $this->isWritePermitted($_SESSION['user']['ProfileID'],'Disposition Override Container')) {
                $json['Success'] = false;
                $json['Result'] = 'You have Read only Access to Disposition Override Container Page';
                return json_encode($json);
            }
            
            $query = "select * from shipping_containers where ShippingContainerID = '".mysqli_real_escape_string($this->connectionlink,$data['ShippingContainerID'])."'";
            $q = mysqli_query($this->connectionlink,$query);
            if(mysqli_error($this->connectionlink)) {			
                $json['Success'] = false;			
                $json['Result'] = mysqli_error($this->connectionlink);			
                return json_encode($json);			
            }
            if(mysqli_affected_rows($this->connectionlink) > 0) {
                $row = mysqli_fetch_assoc($q);
                if($row['StatusID'] != '1' && $row['StatusID'] != '7' && $row['StatusID'] != '6') {
                //if($row['StatusID'] != '1') {
                    $json['Success'] = false;			
                    $json['Result'] = 'Container Status is not Active';
                    return json_encode($json);
                }					
            }
            if($row['SealID'] != $data['SealID']) {
                $json['Success'] = false;			
                $json['Result'] = 'Please Enter Correct Seal ID';
                return json_encode($json);
            }
            $json['Success'] = true;
            $json['Result'] = 'Seal Validated';
            return json_encode($json);
            
		} catch (Exception $e) {
			$json['Success'] = false;
			$json['Result'] = $e->getMessage();
			return json_encode($json);
		}
	}


    public function GetAllUserInputs ($data) {
		if(!isset($_SESSION['user'])) {
			$json['Success'] = false;
			$json['Result'] = 'Login to continue';
			return json_encode($json);
		}
		$json = array(
			'Success' => false,
			'Result' => 'No Data'
		);	
		try {
			// if(! $this->isPermitted($_SESSION['user']['ProfileID'],'Disposition Override')) {
			// 	$json['Success'] = false;
			// 	$json['Result'] = 'No Access to Disposition Override Page';
			// 	return json_encode($json);
			// }
            
            $sql = "Select * from disposition_override_user_input_reasons  order by UserInput";            
            $query = mysqli_query($this->connectionlink,$sql);
            if(mysqli_error($this->connectionlink)) {
                $json['Success'] = false;
                $json['Result'] = mysqli_error($this->connectionlink);
                return json_encode($json);
            } else {
                if(mysqli_affected_rows($this->connectionlink) > 0) {
                    $i = 0;
                    while($row = mysqli_fetch_assoc($query)) {
                        $result[$i] = $row;
                        $i++;
                    }
                    $json['Success'] = true;
                    $json['Result'] = $result;
                } else {
                    $json['Success'] = false;
                    $json['Result'] = 'No Results';
                }
            }
            return json_encode($json);
            
		} catch (Exception $e) {
			$json['Success'] = false;
			$json['Result'] = $e->getMessage();
			return json_encode($json);
		}
	}


    public function ValidateUserInputNotes($Notes) {
        $query = "select count(*) from disposition_override_user_input_reasons where UserInput = '".mysqli_real_escape_string($this->connectionlink,$Notes)."'";
        $q = mysqli_query($this->connectionlink,$query);
        if(mysqli_error($this->connectionlink)) {			
            $json['Success'] = false;			
            $json['Result'] = mysqli_error($this->connectionlink);			
            return json_encode($json);			
        }
        if(mysqli_affected_rows($this->connectionlink) > 0) {
            $row = mysqli_fetch_assoc($q);
            if($row['count(*)'] == 0) {
                $query1 = "insert into disposition_override_user_input_reasons (UserInput,CreatedDate) values ('".mysqli_real_escape_string($this->connectionlink,$Notes)."',NOW())";
                $q1 = mysqli_query($this->connectionlink,$query1);
                if(mysqli_error($this->connectionlink)) {			
                    $json['Success'] = false;			
                    $json['Result'] = mysqli_error($this->connectionlink);			
                    return json_encode($json);			
                }
            }
            return true;
        } else {
            return false;
        }
    }

}
?>